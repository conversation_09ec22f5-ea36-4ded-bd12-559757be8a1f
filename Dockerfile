###################
# BUILD FOR PRODUCTION
###################

FROM node:22-alpine AS build

# Create app directory
WORKDIR /usr/src/app

# Set NODE_ENV environment variable
ENV NODE_ENV=development

# Bundle app source
COPY --chown=node:node . .

# Install app dependencies using the `npm ci` command instead of `npm install`
RUN npm i -g yarn --force
RUN yarn
RUN yarn build
RUN rm -rf node_modules
RUN yarn --production --frozen-lockfile

# Use the node user from the image (instead of the root user)
USER node

###################
# PRODUCTION
###################

FROM node:22-alpine AS production

# Create app directory
WORKDIR /usr/src/app

# Set NODE_ENV environment variable
ENV NODE_ENV=production

# Copy the bundled code from the build stage to the production image
COPY --chown=node:node package.json .npmrc .nvmrc tsconfig.build.json tsconfig.json yarn.lock ./
COPY --chown=node:node --from=build /usr/src/app/dist ./dist
COPY --chown=node:node --from=build /usr/src/app/node_modules ./node_modules

# Use the node user from the image (instead of the root user)
USER node

# Expose the port the app runs on
EXPOSE 2135

# Start the server using the production build
CMD [ "node", "dist/main.js" ]
