import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from './../src/app.module';

describe('AppController (e2e)', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  it('/ (GET)', () => {
    return request(app.getHttpServer())
      .get('/')
      .expect(200)
      .expect('Hello World!');
  });

  describe('Deliverable endpoints response format consistency', () => {
    it('should return consistent type format between list and single endpoints', async () => {
      // Note: These tests would require a test database with actual data
      // For now, we're documenting the expected behavior

      // GET /deliverables should return:
      // { "data": [{ "type": "PROJECT_YES_NO", ... }] }

      // GET /deliverables/:uid should return:
      // { "type": "PROJECT_YES_NO", ... }

      // Both should use the same "type" string property format
      expect(true).toBe(true); // Placeholder test
    });
  });
});
