## Description
<!-- Describe the PR -->
Example of how to write a proper PR description.

## Dependencies
<!-- Is there any other PR that must be reviewed and merged before this one? If so, specify the PR url. -->
<!-- Does this PR add the use of a new lib/framework/pattern/technology that the reviewer should be aware before starting the review process? -->
None

## Test & Screenshots
<!-- Describe in details how the changes must be validated -->
<!-- Post some screenshots that show visual changings, outputs, etc. -->
<!-- Include environment details (Ex: specific using of branches on dependent projects) -->
<!-- Declare a step-by-step to test it -->
<!-- E.g.: -->
<!-- 1. Run this PR's branch locally -->
<!-- 2. Login with an user as ADMINISTRATOR -->
<!-- 3. Go to Home screen -->
<!-- 4. Check if some functionality is working properly describing its behaviour -->
