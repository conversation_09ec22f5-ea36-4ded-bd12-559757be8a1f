# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ampproject/remapping@^2.2.0":
  version "2.3.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@ampproject/remapping/-/remapping-2.3.0.tgz"
  integrity sha1-7UQbb6YAByUgzhi0PSyMyMrsx/Q=
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.24"

"@angular-devkit/core@17.3.11":
  version "17.3.11"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@angular-devkit/core/-/core-17.3.11.tgz"
  integrity sha1-p0sELsBs9ibVovajlxsVbGdZ/gk=
  dependencies:
    ajv "8.12.0"
    ajv-formats "2.1.1"
    jsonc-parser "3.2.1"
    picomatch "4.0.1"
    rxjs "7.8.1"
    source-map "0.7.4"

"@angular-devkit/schematics-cli@17.3.11":
  version "17.3.11"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@angular-devkit/schematics-cli/-/schematics-cli-17.3.11.tgz"
  integrity sha1-3Rlj1ZKufSVV3bushAa6A7zd9P4=
  dependencies:
    "@angular-devkit/core" "17.3.11"
    "@angular-devkit/schematics" "17.3.11"
    ansi-colors "4.1.3"
    inquirer "9.2.15"
    symbol-observable "4.0.0"
    yargs-parser "21.1.1"

"@angular-devkit/schematics@17.3.11":
  version "17.3.11"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@angular-devkit/schematics/-/schematics-17.3.11.tgz"
  integrity sha1-NwlfsIsKsDQ8fA3eV8qBEVF4cU8=
  dependencies:
    "@angular-devkit/core" "17.3.11"
    jsonc-parser "3.2.1"
    magic-string "0.30.8"
    ora "5.4.1"
    rxjs "7.8.1"

"@azure-rest/core-client@^2.3.3":
  version "2.5.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@azure-rest/core-client/-/core-client-2.5.0.tgz"
  integrity sha1-GxmwW9Ej/PTG6oLjlYU1MSCuIxA=
  dependencies:
    "@azure/abort-controller" "^2.0.0"
    "@azure/core-auth" "^1.9.0"
    "@azure/core-rest-pipeline" "^1.5.0"
    "@azure/core-tracing" "^1.0.1"
    "@typespec/ts-http-runtime" "^0.3.0"
    tslib "^2.6.2"

"@azure/abort-controller@^2.0.0", "@azure/abort-controller@^2.1.2":
  version "2.1.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@azure/abort-controller/-/abort-controller-2.1.2.tgz"
  integrity sha1-Qv4MyrI4QdmQWBLFjxCC0neEVm0=
  dependencies:
    tslib "^2.6.2"

"@azure/core-auth@^1.3.0", "@azure/core-auth@^1.4.0", "@azure/core-auth@^1.7.2", "@azure/core-auth@^1.8.0", "@azure/core-auth@^1.9.0":
  version "1.10.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@azure/core-auth/-/core-auth-1.10.0.tgz"
  integrity sha1-aNunA2CA4dnVaZxOSCFKt5b6c60=
  dependencies:
    "@azure/abort-controller" "^2.0.0"
    "@azure/core-util" "^1.11.0"
    tslib "^2.6.2"

"@azure/core-client@^1.3.0", "@azure/core-client@^1.5.0", "@azure/core-client@^1.9.2":
  version "1.10.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@azure/core-client/-/core-client-1.10.0.tgz"
  integrity sha1-n07JyJpjUWknhArmIMYOgRoLVKM=
  dependencies:
    "@azure/abort-controller" "^2.0.0"
    "@azure/core-auth" "^1.4.0"
    "@azure/core-rest-pipeline" "^1.20.0"
    "@azure/core-tracing" "^1.0.0"
    "@azure/core-util" "^1.6.1"
    "@azure/logger" "^1.0.0"
    tslib "^2.6.2"

"@azure/core-http-compat@^2.2.0":
  version "2.3.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@azure/core-http-compat/-/core-http-compat-2.3.0.tgz"
  integrity sha1-6dOWKZIR50IwiCdnQILBO9Y4xr8=
  dependencies:
    "@azure/abort-controller" "^2.0.0"
    "@azure/core-client" "^1.3.0"
    "@azure/core-rest-pipeline" "^1.20.0"

"@azure/core-lro@^2.7.2":
  version "2.7.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@azure/core-lro/-/core-lro-2.7.2.tgz"
  integrity sha1-eHEFAnog5Fx3ZRqYsBpNOwG3Wgg=
  dependencies:
    "@azure/abort-controller" "^2.0.0"
    "@azure/core-util" "^1.2.0"
    "@azure/logger" "^1.0.0"
    tslib "^2.6.2"

"@azure/core-paging@^1.6.2":
  version "1.6.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@azure/core-paging/-/core-paging-1.6.2.tgz"
  integrity sha1-QNOGDcLffykdZjULLP2RcVJkM+c=
  dependencies:
    tslib "^2.6.2"

"@azure/core-rest-pipeline@^1.17.0", "@azure/core-rest-pipeline@^1.19.0", "@azure/core-rest-pipeline@^1.20.0", "@azure/core-rest-pipeline@^1.5.0", "@azure/core-rest-pipeline@^1.8.0":
  version "1.22.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@azure/core-rest-pipeline/-/core-rest-pipeline-1.22.0.tgz"
  integrity sha1-duRKdQk6L0d/xUuE9GBJ3CzmWAA=
  dependencies:
    "@azure/abort-controller" "^2.0.0"
    "@azure/core-auth" "^1.8.0"
    "@azure/core-tracing" "^1.0.1"
    "@azure/core-util" "^1.11.0"
    "@azure/logger" "^1.0.0"
    "@typespec/ts-http-runtime" "^0.3.0"
    tslib "^2.6.2"

"@azure/core-tracing@^1.0.0", "@azure/core-tracing@^1.0.1", "@azure/core-tracing@^1.2.0":
  version "1.3.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@azure/core-tracing/-/core-tracing-1.3.0.tgz"
  integrity sha1-NBFT9bKSdTnriYV3ZR7kjOmN2iU=
  dependencies:
    tslib "^2.6.2"

"@azure/core-util@^1.10.0", "@azure/core-util@^1.11.0", "@azure/core-util@^1.2.0", "@azure/core-util@^1.6.1":
  version "1.13.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@azure/core-util/-/core-util-1.13.0.tgz"
  integrity sha1-/Cg0/FHh4rt0twwoS0D4JNhnQio=
  dependencies:
    "@azure/abort-controller" "^2.0.0"
    "@typespec/ts-http-runtime" "^0.3.0"
    tslib "^2.6.2"

"@azure/identity@^4.2.1":
  version "4.11.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@azure/identity/-/identity-4.11.1.tgz"
  integrity sha1-GbpbdgGuTy3tAQxVylUgD/pseew=
  dependencies:
    "@azure/abort-controller" "^2.0.0"
    "@azure/core-auth" "^1.9.0"
    "@azure/core-client" "^1.9.2"
    "@azure/core-rest-pipeline" "^1.17.0"
    "@azure/core-tracing" "^1.0.0"
    "@azure/core-util" "^1.11.0"
    "@azure/logger" "^1.0.0"
    "@azure/msal-browser" "^4.2.0"
    "@azure/msal-node" "^3.5.0"
    open "^10.1.0"
    tslib "^2.2.0"

"@azure/keyvault-common@^2.0.0":
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@azure/keyvault-common/-/keyvault-common-2.0.0.tgz"
  integrity sha1-keUN8B2b+o9V8Qe7nNvFdYaysqQ=
  dependencies:
    "@azure/abort-controller" "^2.0.0"
    "@azure/core-auth" "^1.3.0"
    "@azure/core-client" "^1.5.0"
    "@azure/core-rest-pipeline" "^1.8.0"
    "@azure/core-tracing" "^1.0.0"
    "@azure/core-util" "^1.10.0"
    "@azure/logger" "^1.1.4"
    tslib "^2.2.0"

"@azure/keyvault-keys@^4.4.0":
  version "4.10.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@azure/keyvault-keys/-/keyvault-keys-4.10.0.tgz"
  integrity sha1-dUdujyhYDcI7vJ2sbRZU4THW79U=
  dependencies:
    "@azure-rest/core-client" "^2.3.3"
    "@azure/abort-controller" "^2.1.2"
    "@azure/core-auth" "^1.9.0"
    "@azure/core-http-compat" "^2.2.0"
    "@azure/core-lro" "^2.7.2"
    "@azure/core-paging" "^1.6.2"
    "@azure/core-rest-pipeline" "^1.19.0"
    "@azure/core-tracing" "^1.2.0"
    "@azure/core-util" "^1.11.0"
    "@azure/keyvault-common" "^2.0.0"
    "@azure/logger" "^1.1.4"
    tslib "^2.8.1"

"@azure/logger@^1.0.0", "@azure/logger@^1.1.4":
  version "1.3.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@azure/logger/-/logger-1.3.0.tgz"
  integrity sha1-VQHPhdT1JjBgKozHXfdlaMlpqCc=
  dependencies:
    "@typespec/ts-http-runtime" "^0.3.0"
    tslib "^2.6.2"

"@azure/msal-browser@^4.2.0":
  version "4.19.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@azure/msal-browser/-/msal-browser-4.19.0.tgz"
  integrity sha1-L0DP+BsvVF0p4K7/h7lfL52rUbw=
  dependencies:
    "@azure/msal-common" "15.10.0"

"@azure/msal-common@15.10.0":
  version "15.10.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@azure/msal-common/-/msal-common-15.10.0.tgz"
  integrity sha1-6gqgPx28kgkOofuMteoy31bU+Io=

"@azure/msal-node@^3.5.0":
  version "3.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@azure/msal-node/-/msal-node-3.7.0.tgz"
  integrity sha1-qct36bysoi20VzURQzlJ8Ufnw5Y=
  dependencies:
    "@azure/msal-common" "15.10.0"
    jsonwebtoken "^9.0.0"
    uuid "^8.3.0"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.12.13", "@babel/code-frame@^7.16.7", "@babel/code-frame@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/code-frame/-/code-frame-7.27.1.tgz"
  integrity sha1-IA9xXmbVKiOyIalDVTSpHME61b4=
  dependencies:
    "@babel/helper-validator-identifier" "^7.27.1"
    js-tokens "^4.0.0"
    picocolors "^1.1.1"

"@babel/compat-data@^7.27.2":
  version "7.28.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/compat-data/-/compat-data-7.28.0.tgz"
  integrity sha1-n8b9WMKmoVJDzROYMiSWg5IHB5A=

"@babel/core@^7.11.6", "@babel/core@^7.12.3", "@babel/core@^7.23.9":
  version "7.28.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/core/-/core-7.28.0.tgz"
  integrity sha1-VdrYCNW/NEWhCO78iOo/3wNHSaQ=
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.27.1"
    "@babel/generator" "^7.28.0"
    "@babel/helper-compilation-targets" "^7.27.2"
    "@babel/helper-module-transforms" "^7.27.3"
    "@babel/helpers" "^7.27.6"
    "@babel/parser" "^7.28.0"
    "@babel/template" "^7.27.2"
    "@babel/traverse" "^7.28.0"
    "@babel/types" "^7.28.0"
    convert-source-map "^2.0.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.3"
    semver "^6.3.1"

"@babel/generator@^7.28.0", "@babel/generator@^7.7.2":
  version "7.28.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/generator/-/generator-7.28.0.tgz"
  integrity sha1-nML3vW6wVNd9xmwmZBSKDFEYrNI=
  dependencies:
    "@babel/parser" "^7.28.0"
    "@babel/types" "^7.28.0"
    "@jridgewell/gen-mapping" "^0.3.12"
    "@jridgewell/trace-mapping" "^0.3.28"
    jsesc "^3.0.2"

"@babel/helper-compilation-targets@^7.27.2":
  version "7.27.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz"
  integrity sha1-RqD276uAjVHSnOloWN0Qzocycz0=
  dependencies:
    "@babel/compat-data" "^7.27.2"
    "@babel/helper-validator-option" "^7.27.1"
    browserslist "^4.24.0"
    lru-cache "^5.1.1"
    semver "^6.3.1"

"@babel/helper-globals@^7.28.0":
  version "7.28.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/helper-globals/-/helper-globals-7.28.0.tgz"
  integrity sha1-uUMN8qpOF7woZl6t6uiqHZheZnQ=

"@babel/helper-module-imports@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz"
  integrity sha1-fvdpoyPiZV4SZnO7bS1pE7vq0gQ=
  dependencies:
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helper-module-transforms@^7.27.3":
  version "7.27.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/helper-module-transforms/-/helper-module-transforms-7.27.3.tgz"
  integrity sha1-2wu8+6WAL573hwcFp++HiFCO3gI=
  dependencies:
    "@babel/helper-module-imports" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"
    "@babel/traverse" "^7.27.3"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.14.5", "@babel/helper-plugin-utils@^7.27.1", "@babel/helper-plugin-utils@^7.8.0":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz"
  integrity sha1-3bL4dlNP+AE+bCspm/TTmzxR1Ew=

"@babel/helper-string-parser@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz"
  integrity sha1-VNp5YJerGc5n7Z+ItHuy7Ek2doc=

"@babel/helper-validator-identifier@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz"
  integrity sha1-pwVNzBRaln3U3I/uhFpXwTFsnfg=

"@babel/helper-validator-option@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz"
  integrity sha1-+lL1sefbGrBJRFtCHERxMDiXcC8=

"@babel/helpers@^7.27.6":
  version "7.28.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/helpers/-/helpers-7.28.2.tgz"
  integrity sha1-gPCRj+y/6+qa+FbEGXYyMAQO6FA=
  dependencies:
    "@babel/template" "^7.27.2"
    "@babel/types" "^7.28.2"

"@babel/parser@^7.1.0", "@babel/parser@^7.14.7", "@babel/parser@^7.20.7", "@babel/parser@^7.23.9", "@babel/parser@^7.27.2", "@babel/parser@^7.28.0":
  version "7.28.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/parser/-/parser-7.28.0.tgz"
  integrity sha1-l5gp+6tRop4TkB5agHE9vLhAgl4=
  dependencies:
    "@babel/types" "^7.28.0"

"@babel/plugin-syntax-async-generators@^7.8.4":
  version "7.8.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz"
  integrity sha1-qYP7Gusuw/btBCohD2QOkOeG/g0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-bigint@^7.8.3":
  version "7.8.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.8.3.tgz"
  integrity sha1-TJpvZp9dDN8bkKFnHpoUa+UwDOo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.12.13":
  version "7.12.13"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz"
  integrity sha1-tcmHJ0xKOoK4lxR5aTGmtTVErhA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-class-static-block@^7.14.5":
  version "7.14.5"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz"
  integrity sha1-GV34mxRrS3izv4l/16JXyEZZ1AY=
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-import-attributes@^7.24.7":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.27.1.tgz"
  integrity sha1-NMAX1USW+bEbYUdOfqPf1VY//gc=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-import-meta@^7.10.4":
  version "7.10.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz"
  integrity sha1-7mATSMNw+jNNIge+FYd3SWUh/VE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-json-strings@^7.8.3":
  version "7.8.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz"
  integrity sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-jsx@^7.7.2":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.27.1.tgz"
  integrity sha1-L5vrXv8w+lB8VTLRB9qse4iPo0w=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-logical-assignment-operators@^7.10.4":
  version "7.10.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz"
  integrity sha1-ypHvRjA1MESLkGZSusLp/plB9pk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  version "7.8.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz"
  integrity sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.10.4":
  version "7.10.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz"
  integrity sha1-ubBws+M1cM2f0Hun+pHA3Te5r5c=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.8.3":
  version "7.8.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz"
  integrity sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  version "7.8.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz"
  integrity sha1-YRGiZbz7Ag6579D9/X0mQCue1sE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.8.3":
  version "7.8.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz"
  integrity sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-private-property-in-object@^7.14.5":
  version "7.14.5"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz"
  integrity sha1-DcZnHsDqIrbpShEU+FeXDNOd4a0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-top-level-await@^7.14.5":
  version "7.14.5"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz"
  integrity sha1-wc/a3DWmRiQAAfBhOCR7dBw02Uw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-typescript@^7.7.2":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.27.1.tgz"
  integrity sha1-UUfSkGank0UPIgxj+jqUMbfm3Rg=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/template@^7.27.2", "@babel/template@^7.3.3":
  version "7.27.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/template/-/template-7.27.2.tgz"
  integrity sha1-+njO7TxOe2Pr9ss55YUvykX2gJ0=
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/parser" "^7.27.2"
    "@babel/types" "^7.27.1"

"@babel/traverse@^7.27.1", "@babel/traverse@^7.27.3", "@babel/traverse@^7.28.0":
  version "7.28.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/traverse/-/traverse-7.28.0.tgz"
  integrity sha1-UYqhEzWbBiBCN54zPbGDgLU340s=
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/generator" "^7.28.0"
    "@babel/helper-globals" "^7.28.0"
    "@babel/parser" "^7.28.0"
    "@babel/template" "^7.27.2"
    "@babel/types" "^7.28.0"
    debug "^4.3.1"

"@babel/types@^7.0.0", "@babel/types@^7.20.7", "@babel/types@^7.27.1", "@babel/types@^7.28.0", "@babel/types@^7.28.2", "@babel/types@^7.3.3":
  version "7.28.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/types/-/types-7.28.2.tgz"
  integrity sha1-2p2whWqaiOChOwGYgddRNYjPcSs=
  dependencies:
    "@babel/helper-string-parser" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"

"@bcoe/v8-coverage@^0.2.3":
  version "0.2.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@bcoe/v8-coverage/-/v8-coverage-0.2.3.tgz"
  integrity sha1-daLotRy3WKdVPWgEpZMteqznXDk=

"@colors/colors@1.5.0":
  version "1.5.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@colors/colors/-/colors-1.5.0.tgz"
  integrity sha1-u1BFecHK6SPmV2pPXaQ9Jfl729k=

"@cspotcode/source-map-support@^0.8.0":
  version "0.8.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@cspotcode/source-map-support/-/source-map-support-0.8.1.tgz"
  integrity sha1-AGKcNaaI4FqIsc2mhPudXnPwAKE=
  dependencies:
    "@jridgewell/trace-mapping" "0.3.9"

"@datadog/libdatadog@0.7.0":
  version "0.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@datadog/libdatadog/-/libdatadog-0.7.0.tgz"
  integrity sha1-geB9MEDGKIkttpfM0BrjxNKnYxU=

"@datadog/native-appsec@10.0.1":
  version "10.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@datadog/native-appsec/-/native-appsec-10.0.1.tgz"
  integrity sha1-IgvYVZcevjxReRWm20yuxQVFsSg=
  dependencies:
    node-gyp-build "^3.9.0"

"@datadog/native-iast-taint-tracking@4.0.0":
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@datadog/native-iast-taint-tracking/-/native-iast-taint-tracking-4.0.0.tgz"
  integrity sha1-p3R0LmcjuT1YvzGodyjk2hY0B08=
  dependencies:
    node-gyp-build "^3.9.0"

"@datadog/native-metrics@3.1.1":
  version "3.1.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@datadog/native-metrics/-/native-metrics-3.1.1.tgz"
  integrity sha1-TlyXdXUa8T41PmTlc6tyQQRTjO4=
  dependencies:
    node-addon-api "^6.1.0"
    node-gyp-build "^3.9.0"

"@datadog/pprof@5.9.0":
  version "5.9.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@datadog/pprof/-/pprof-5.9.0.tgz"
  integrity sha1-vqzRUHME40kJAP856hCOfqdykhs=
  dependencies:
    delay "^5.0.0"
    node-gyp-build "<4.0"
    p-limit "^3.1.0"
    pprof-format "^2.1.0"
    source-map "^0.7.4"

"@datadog/sketches-js@2.1.1":
  version "2.1.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@datadog/sketches-js/-/sketches-js-2.1.1.tgz"
  integrity sha1-nsIlGzyTK09D4dFkRh+my28ot9A=

"@datadog/wasm-js-rewriter@4.0.1":
  version "4.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@datadog/wasm-js-rewriter/-/wasm-js-rewriter-4.0.1.tgz"
  integrity sha1-iDU16X9riLFUJ/k7XcXS06AcArY=
  dependencies:
    js-yaml "^4.1.0"
    lru-cache "^7.14.0"
    module-details-from-path "^1.0.3"
    node-gyp-build "^4.5.0"

"@eslint-community/eslint-utils@^4.2.0", "@eslint-community/eslint-utils@^4.4.0":
  version "4.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@eslint-community/eslint-utils/-/eslint-utils-4.7.0.tgz"
  integrity sha1-YHCEYwxsAzmSoILebm+8GotSF1o=
  dependencies:
    eslint-visitor-keys "^3.4.3"

"@eslint-community/regexpp@^4.10.0", "@eslint-community/regexpp@^4.6.1":
  version "4.12.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@eslint-community/regexpp/-/regexpp-4.12.1.tgz"
  integrity sha1-z8bP/jnfOQo4Qc3iq8z5Lqp64OA=

"@eslint/eslintrc@^2.1.4":
  version "2.1.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@eslint/eslintrc/-/eslintrc-2.1.4.tgz"
  integrity sha1-OIomnw8lwbatwxe1osVXFIlMcK0=
  dependencies:
    ajv "^6.12.4"
    debug "^4.3.2"
    espree "^9.6.0"
    globals "^13.19.0"
    ignore "^5.2.0"
    import-fresh "^3.2.1"
    js-yaml "^4.1.0"
    minimatch "^3.1.2"
    strip-json-comments "^3.1.1"

"@eslint/js@8.57.1":
  version "8.57.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@eslint/js/-/js-8.57.1.tgz"
  integrity sha1-3mM9s+wu9qPIni8ZA4Bj6KEi4sI=

"@ghq-abi/northstar-api-libs@^1.0.0":
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@ghq-abi/northstar-api-libs/-/northstar-api-libs-1.0.0.tgz"
  integrity sha1-uX4L0ilMzIvdAnQobJ2FzZFiUYo=
  dependencies:
    "@nestjs/common" "^11.1.5"
    "@nestjs/core" "^11.1.5"
    dotenv "^17.2.1"
    redis "^5.8.0"

"@ghq-abi/northstar-domain@^0.0.3":
  version "0.0.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@ghq-abi/northstar-domain/-/northstar-domain-0.0.3.tgz"
  integrity sha1-SBFAozv/M/PpemAasuESMx/7ybQ=
  dependencies:
    "@nestjs/common" "^11.1.5"
    "@nestjs/config" "^4.0.2"
    "@nestjs/core" "^11.1.6"
    "@nestjs/typeorm" "^11.0.0"
    dotenv "^17.2.1"
    mssql "^11.0.1"
    typeorm "^0.3.25"

"@humanwhocodes/config-array@^0.13.0":
  version "0.13.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@humanwhocodes/config-array/-/config-array-0.13.0.tgz"
  integrity sha1-+5B2JN8yVtBLmqLfUNeql+xkh0g=
  dependencies:
    "@humanwhocodes/object-schema" "^2.0.3"
    debug "^4.3.1"
    minimatch "^3.0.5"

"@humanwhocodes/module-importer@^1.0.1":
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz"
  integrity sha1-r1smkaIrRL6EewyoFkHF+2rQFyw=

"@humanwhocodes/object-schema@^2.0.3":
  version "2.0.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@humanwhocodes/object-schema/-/object-schema-2.0.3.tgz"
  integrity sha1-Siho111taWPkI7z5C3/RvjQ0CdM=

"@isaacs/cliui@^8.0.2":
  version "8.0.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@isaacs/cliui/-/cliui-8.0.2.tgz"
  integrity sha1-s3Znt7wYHBaHgiWbq0JHT79StVA=
  dependencies:
    string-width "^5.1.2"
    string-width-cjs "npm:string-width@^4.2.0"
    strip-ansi "^7.0.1"
    strip-ansi-cjs "npm:strip-ansi@^6.0.1"
    wrap-ansi "^8.1.0"
    wrap-ansi-cjs "npm:wrap-ansi@^7.0.0"

"@isaacs/ttlcache@^1.4.1":
  version "1.4.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@isaacs/ttlcache/-/ttlcache-1.4.1.tgz"
  integrity sha1-Ifsj2zTptiIMa6AjoBGKLdNGHqI=

"@istanbuljs/load-nyc-config@^1.0.0":
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@istanbuljs/load-nyc-config/-/load-nyc-config-1.1.0.tgz"
  integrity sha1-/T2x1Z7PfPEh6AZQu4ZxL5tV7O0=
  dependencies:
    camelcase "^5.3.1"
    find-up "^4.1.0"
    get-package-type "^0.1.0"
    js-yaml "^3.13.1"
    resolve-from "^5.0.0"

"@istanbuljs/schema@^0.1.2", "@istanbuljs/schema@^0.1.3":
  version "0.1.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@istanbuljs/schema/-/schema-0.1.3.tgz"
  integrity sha1-5F44TkuOwWvOL9kDr3hFD2v37Jg=

"@jest/console@^29.7.0":
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jest/console/-/console-29.7.0.tgz"
  integrity sha1-zUgi29uEUpJlxaK9tSmjycyVD/w=
  dependencies:
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    chalk "^4.0.0"
    jest-message-util "^29.7.0"
    jest-util "^29.7.0"
    slash "^3.0.0"

"@jest/core@^29.7.0":
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jest/core/-/core-29.7.0.tgz"
  integrity sha1-tszMI58w/zZglljFpeIpF1fORI8=
  dependencies:
    "@jest/console" "^29.7.0"
    "@jest/reporters" "^29.7.0"
    "@jest/test-result" "^29.7.0"
    "@jest/transform" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    ansi-escapes "^4.2.1"
    chalk "^4.0.0"
    ci-info "^3.2.0"
    exit "^0.1.2"
    graceful-fs "^4.2.9"
    jest-changed-files "^29.7.0"
    jest-config "^29.7.0"
    jest-haste-map "^29.7.0"
    jest-message-util "^29.7.0"
    jest-regex-util "^29.6.3"
    jest-resolve "^29.7.0"
    jest-resolve-dependencies "^29.7.0"
    jest-runner "^29.7.0"
    jest-runtime "^29.7.0"
    jest-snapshot "^29.7.0"
    jest-util "^29.7.0"
    jest-validate "^29.7.0"
    jest-watcher "^29.7.0"
    micromatch "^4.0.4"
    pretty-format "^29.7.0"
    slash "^3.0.0"
    strip-ansi "^6.0.0"

"@jest/environment@^29.7.0":
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jest/environment/-/environment-29.7.0.tgz"
  integrity sha1-JNYfVP8feG881Ac7S5RBY4O68qc=
  dependencies:
    "@jest/fake-timers" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    jest-mock "^29.7.0"

"@jest/expect-utils@^29.7.0":
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jest/expect-utils/-/expect-utils-29.7.0.tgz"
  integrity sha1-Aj7+XSaopw8hZ30KGvwPCkTjocY=
  dependencies:
    jest-get-type "^29.6.3"

"@jest/expect@^29.7.0":
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jest/expect/-/expect-29.7.0.tgz"
  integrity sha1-dqPtsMt1O3Dfv+Iyg1ENPUVDK/I=
  dependencies:
    expect "^29.7.0"
    jest-snapshot "^29.7.0"

"@jest/fake-timers@^29.7.0":
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jest/fake-timers/-/fake-timers-29.7.0.tgz"
  integrity sha1-/ZG/H/+xbX0NJKQmqxpHpJiBpWU=
  dependencies:
    "@jest/types" "^29.6.3"
    "@sinonjs/fake-timers" "^10.0.2"
    "@types/node" "*"
    jest-message-util "^29.7.0"
    jest-mock "^29.7.0"
    jest-util "^29.7.0"

"@jest/globals@^29.7.0":
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jest/globals/-/globals-29.7.0.tgz"
  integrity sha1-jZKQ+exH/3cmB/qGTKHVou+uHU0=
  dependencies:
    "@jest/environment" "^29.7.0"
    "@jest/expect" "^29.7.0"
    "@jest/types" "^29.6.3"
    jest-mock "^29.7.0"

"@jest/reporters@^29.7.0":
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jest/reporters/-/reporters-29.7.0.tgz"
  integrity sha1-BLJi7LO4+qg7Cz0yFiOXI5Po9Mc=
  dependencies:
    "@bcoe/v8-coverage" "^0.2.3"
    "@jest/console" "^29.7.0"
    "@jest/test-result" "^29.7.0"
    "@jest/transform" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@jridgewell/trace-mapping" "^0.3.18"
    "@types/node" "*"
    chalk "^4.0.0"
    collect-v8-coverage "^1.0.0"
    exit "^0.1.2"
    glob "^7.1.3"
    graceful-fs "^4.2.9"
    istanbul-lib-coverage "^3.0.0"
    istanbul-lib-instrument "^6.0.0"
    istanbul-lib-report "^3.0.0"
    istanbul-lib-source-maps "^4.0.0"
    istanbul-reports "^3.1.3"
    jest-message-util "^29.7.0"
    jest-util "^29.7.0"
    jest-worker "^29.7.0"
    slash "^3.0.0"
    string-length "^4.0.1"
    strip-ansi "^6.0.0"
    v8-to-istanbul "^9.0.1"

"@jest/schemas@^29.6.3":
  version "29.6.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jest/schemas/-/schemas-29.6.3.tgz"
  integrity sha1-Qwtc6KTgBEp+OBlmMwWnswkcjgM=
  dependencies:
    "@sinclair/typebox" "^0.27.8"

"@jest/source-map@^29.6.3":
  version "29.6.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jest/source-map/-/source-map-29.6.3.tgz"
  integrity sha1-2Quncglc83o0peuUE/G1YqCFVMQ=
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.18"
    callsites "^3.0.0"
    graceful-fs "^4.2.9"

"@jest/test-result@^29.7.0":
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jest/test-result/-/test-result-29.7.0.tgz"
  integrity sha1-jbmoCqGgl7siYlcmhnNLrtmxZXw=
  dependencies:
    "@jest/console" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/istanbul-lib-coverage" "^2.0.0"
    collect-v8-coverage "^1.0.0"

"@jest/test-sequencer@^29.7.0":
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jest/test-sequencer/-/test-sequencer-29.7.0.tgz"
  integrity sha1-bO+XfOHTmDSjrqiHoXJmKKbwcs4=
  dependencies:
    "@jest/test-result" "^29.7.0"
    graceful-fs "^4.2.9"
    jest-haste-map "^29.7.0"
    slash "^3.0.0"

"@jest/transform@^29.7.0":
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jest/transform/-/transform-29.7.0.tgz"
  integrity sha1-3y3Zw0bH13aLigZjmZRkDGQuKEw=
  dependencies:
    "@babel/core" "^7.11.6"
    "@jest/types" "^29.6.3"
    "@jridgewell/trace-mapping" "^0.3.18"
    babel-plugin-istanbul "^6.1.1"
    chalk "^4.0.0"
    convert-source-map "^2.0.0"
    fast-json-stable-stringify "^2.1.0"
    graceful-fs "^4.2.9"
    jest-haste-map "^29.7.0"
    jest-regex-util "^29.6.3"
    jest-util "^29.7.0"
    micromatch "^4.0.4"
    pirates "^4.0.4"
    slash "^3.0.0"
    write-file-atomic "^4.0.2"

"@jest/types@^29.6.3":
  version "29.6.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jest/types/-/types-29.6.3.tgz"
  integrity sha1-ETH4z2NOfoTF53urEvBSr1hfulk=
  dependencies:
    "@jest/schemas" "^29.6.3"
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^3.0.0"
    "@types/node" "*"
    "@types/yargs" "^17.0.8"
    chalk "^4.0.0"

"@jridgewell/gen-mapping@^0.3.12", "@jridgewell/gen-mapping@^0.3.5":
  version "0.3.12"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jridgewell/gen-mapping/-/gen-mapping-0.3.12.tgz"
  integrity sha1-IjTOJsYoifA9s9f+pDwZMqs+kns=
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.5.0"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.0.3", "@jridgewell/resolve-uri@^3.1.0":
  version "3.1.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz"
  integrity sha1-eg7mAfYPmaIMfHxf8MgDiMEYm9Y=

"@jridgewell/source-map@^0.3.3":
  version "0.3.10"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jridgewell/source-map/-/source-map-0.3.10.tgz"
  integrity sha1-o1cURGouhFA/+b/mbx0dSEbyB1s=
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.14", "@jridgewell/sourcemap-codec@^1.4.15", "@jridgewell/sourcemap-codec@^1.5.0":
  version "1.5.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.4.tgz"
  integrity sha1-c1gENDOy5dpWmqAsvEwSHaOvJ9c=

"@jridgewell/trace-mapping@^0.3.12", "@jridgewell/trace-mapping@^0.3.18", "@jridgewell/trace-mapping@^0.3.24", "@jridgewell/trace-mapping@^0.3.25", "@jridgewell/trace-mapping@^0.3.28":
  version "0.3.29"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jridgewell/trace-mapping/-/trace-mapping-0.3.29.tgz"
  integrity sha1-pY0x6q2vksZpVoCy4dRkqbj79/w=
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@jridgewell/trace-mapping@0.3.9":
  version "0.3.9"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jridgewell/trace-mapping/-/trace-mapping-0.3.9.tgz"
  integrity sha1-ZTT9WTOlO6fL86F2FeJzoNEnP/k=
  dependencies:
    "@jridgewell/resolve-uri" "^3.0.3"
    "@jridgewell/sourcemap-codec" "^1.4.10"

"@js-joda/core@^5.6.1":
  version "5.6.5"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@js-joda/core/-/core-5.6.5.tgz"
  integrity sha1-x2aJS0nrgERIC5FiX7fcNw6Bgu8=

"@jsep-plugin/assignment@^1.3.0":
  version "1.3.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jsep-plugin/assignment/-/assignment-1.3.0.tgz"
  integrity sha1-/PxUF6BJM/fO7nhuirSYqjziskI=

"@jsep-plugin/regex@^1.0.4":
  version "1.0.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jsep-plugin/regex/-/regex-1.0.4.tgz"
  integrity sha1-yy/EIyIPpxxgkyO5un99NEp1X8w=

"@ljharb/through@^2.3.12":
  version "2.3.14"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@ljharb/through/-/through-2.3.14.tgz"
  integrity sha1-pd9EKV9E3CO/4QavWUJt0Gd3YLE=
  dependencies:
    call-bind "^1.0.8"

"@lukeed/csprng@^1.0.0":
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@lukeed/csprng/-/csprng-1.1.0.tgz"
  integrity sha1-Hj5L0Fwcx6Cy3b2KA/OfbktebP4=

"@microsoft/tsdoc@^0.15.0":
  version "0.15.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@microsoft/tsdoc/-/tsdoc-0.15.1.tgz"
  integrity sha1-1PaTc1O8RWgpJlTvsKDgUyrby6I=

"@nestjs/cli@^10.0.0":
  version "10.4.9"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@nestjs/cli/-/cli-10.4.9.tgz"
  integrity sha1-rDojCWpHJUZTYNjWCBDz6Ff0qAM=
  dependencies:
    "@angular-devkit/core" "17.3.11"
    "@angular-devkit/schematics" "17.3.11"
    "@angular-devkit/schematics-cli" "17.3.11"
    "@nestjs/schematics" "^10.0.1"
    chalk "4.1.2"
    chokidar "3.6.0"
    cli-table3 "0.6.5"
    commander "4.1.1"
    fork-ts-checker-webpack-plugin "9.0.2"
    glob "10.4.5"
    inquirer "8.2.6"
    node-emoji "1.11.0"
    ora "5.4.1"
    tree-kill "1.2.2"
    tsconfig-paths "4.2.0"
    tsconfig-paths-webpack-plugin "4.2.0"
    typescript "5.7.2"
    webpack "5.97.1"
    webpack-node-externals "3.0.0"

"@nestjs/common@^11.0.0", "@nestjs/common@^11.1.5":
  version "11.1.6"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@nestjs/common/-/common-11.1.6.tgz"
  integrity sha1-cEribwnM0TW/Pm9Etu9ONAfqPFQ=
  dependencies:
    file-type "21.0.0"
    iterare "1.2.1"
    load-esm "1.0.2"
    tslib "2.8.1"
    uid "2.0.2"

"@nestjs/config@^3.3.0":
  version "3.3.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@nestjs/config/-/config-3.3.0.tgz"
  integrity sha1-3cUguiaoRT7l5pDhj7ezXpuseXQ=
  dependencies:
    dotenv "16.4.5"
    dotenv-expand "10.0.0"
    lodash "4.17.21"

"@nestjs/config@^4.0.2":
  version "4.0.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@nestjs/config/-/config-4.0.2.tgz"
  integrity sha1-ond6H9LQ1ZS6s5U/UPvKlcFMzlI=
  dependencies:
    dotenv "16.4.7"
    dotenv-expand "12.0.1"
    lodash "4.17.21"

"@nestjs/core@^10.0.0":
  version "10.4.20"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@nestjs/core/-/core-10.4.20.tgz"
  integrity sha1-J0C5H2C1s4luO8paGJBk57wVwvw=
  dependencies:
    "@nuxtjs/opencollective" "0.3.2"
    fast-safe-stringify "2.1.1"
    iterare "1.2.1"
    path-to-regexp "3.3.0"
    tslib "2.8.1"
    uid "2.0.2"

"@nestjs/core@^11.1.5":
  version "11.1.6"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@nestjs/core/-/core-11.1.6.tgz"
  integrity sha1-nVSILxIRaLL6Kwf6HbCFgWGoBiY=
  dependencies:
    "@nuxt/opencollective" "0.4.1"
    fast-safe-stringify "2.1.1"
    iterare "1.2.1"
    path-to-regexp "8.2.0"
    tslib "2.8.1"
    uid "2.0.2"

"@nestjs/core@^11.1.6":
  version "11.1.6"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@nestjs/core/-/core-11.1.6.tgz"
  integrity sha1-nVSILxIRaLL6Kwf6HbCFgWGoBiY=
  dependencies:
    "@nuxt/opencollective" "0.4.1"
    fast-safe-stringify "2.1.1"
    iterare "1.2.1"
    path-to-regexp "8.2.0"
    tslib "2.8.1"
    uid "2.0.2"

"@nestjs/jwt@^10.2.0":
  version "10.2.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@nestjs/jwt/-/jwt-10.2.0.tgz"
  integrity sha1-aqNaBJItGcZCbvztRnFiD5Lm29A=
  dependencies:
    "@types/jsonwebtoken" "9.0.5"
    jsonwebtoken "9.0.2"

"@nestjs/mapped-types@2.0.6":
  version "2.0.6"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@nestjs/mapped-types/-/mapped-types-2.0.6.tgz"
  integrity sha1-0thSNwn9XYcqm54MOBYnRuKn9E4=

"@nestjs/passport@^10.0.3":
  version "10.0.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@nestjs/passport/-/passport-10.0.3.tgz"
  integrity sha1-JuxbIWfTZOBJYsEV/O+A0Q4YU2c=

"@nestjs/platform-express@^11.1.5":
  version "11.1.6"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@nestjs/platform-express/-/platform-express-11.1.6.tgz"
  integrity sha1-mx3PgqOz/dV2HJGK1mSv+D5OrMc=
  dependencies:
    cors "2.8.5"
    express "5.1.0"
    multer "2.0.2"
    path-to-regexp "8.2.0"
    tslib "2.8.1"

"@nestjs/schematics@^10.0.0", "@nestjs/schematics@^10.0.1":
  version "10.2.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@nestjs/schematics/-/schematics-10.2.3.tgz"
  integrity sha1-YFP0PFBlueglzQjE2xv2vLyaamI=
  dependencies:
    "@angular-devkit/core" "17.3.11"
    "@angular-devkit/schematics" "17.3.11"
    comment-json "4.2.5"
    jsonc-parser "3.3.1"
    pluralize "8.0.0"

"@nestjs/swagger@^8.0.7":
  version "8.1.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@nestjs/swagger/-/swagger-8.1.1.tgz"
  integrity sha1-GNYamVtrF3WbyjYUdJBNacRAYJM=
  dependencies:
    "@microsoft/tsdoc" "^0.15.0"
    "@nestjs/mapped-types" "2.0.6"
    js-yaml "4.1.0"
    lodash "4.17.21"
    path-to-regexp "3.3.0"
    swagger-ui-dist "5.18.2"

"@nestjs/testing@^10.0.0":
  version "10.4.20"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@nestjs/testing/-/testing-10.4.20.tgz"
  integrity sha1-RPk2IbYpBZos4DRXVrdmZkxTKoM=
  dependencies:
    tslib "2.8.1"

"@nestjs/typeorm@^10.0.2":
  version "10.0.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@nestjs/typeorm/-/typeorm-10.0.2.tgz"
  integrity sha1-JePsPJoSewhcBv1+ol+GkNuhRcI=
  dependencies:
    uuid "9.0.1"

"@nestjs/typeorm@^11.0.0":
  version "11.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@nestjs/typeorm/-/typeorm-11.0.0.tgz"
  integrity sha1-sPRdaQI5bbieCsH05zjC/zQHt5Q=

"@noble/hashes@^1.1.5":
  version "1.8.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@noble/hashes/-/hashes-1.8.0.tgz"
  integrity sha1-zuQ9gB/O+WRLEbgZSFdpWs1fgVo=

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz"
  integrity sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@^2.0.2", "@nodelib/fs.stat@2.0.5":
  version "2.0.5"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz"
  integrity sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=

"@nodelib/fs.walk@^1.2.3", "@nodelib/fs.walk@^1.2.8":
  version "1.2.8"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz"
  integrity sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@nuxt/opencollective@0.4.1":
  version "0.4.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@nuxt/opencollective/-/opencollective-0.4.1.tgz"
  integrity sha1-V7xB0rA7L7oguTXBWVCsD0vSzqI=
  dependencies:
    consola "^3.2.3"

"@nuxtjs/opencollective@0.3.2":
  version "0.3.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@nuxtjs/opencollective/-/opencollective-0.3.2.tgz"
  integrity sha1-YgzhBE96x3GF6CXhk2EVuzjiaBw=
  dependencies:
    chalk "^4.1.0"
    consola "^2.15.0"
    node-fetch "^2.6.1"

"@opentelemetry/api@1.8.0":
  version "1.8.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@opentelemetry/api/-/api-1.8.0.tgz"
  integrity sha1-WqertI8j9pMGjtKZmuYn0vfZAuw=

"@opentelemetry/core@^1.14.0":
  version "1.30.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@opentelemetry/core/-/core-1.30.1.tgz"
  integrity sha1-oLRouzljWN+AGIFwnqOCmfwwqyc=
  dependencies:
    "@opentelemetry/semantic-conventions" "1.28.0"

"@opentelemetry/semantic-conventions@1.28.0":
  version "1.28.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@opentelemetry/semantic-conventions/-/semantic-conventions-1.28.0.tgz"
  integrity sha1-M3+yvKBFPQcmaW50X1AGRBH2RtY=

"@paralleldrive/cuid2@^2.2.2":
  version "2.2.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@paralleldrive/cuid2/-/cuid2-2.2.2.tgz"
  integrity sha1-f5E2TVO4niycueAujdDxKeg0RV8=
  dependencies:
    "@noble/hashes" "^1.1.5"

"@pkgjs/parseargs@^0.11.0":
  version "0.11.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@pkgjs/parseargs/-/parseargs-0.11.0.tgz"
  integrity sha1-p36nQvqyV3UUVDTrHSMoz1ATrDM=

"@pkgr/core@^0.2.9":
  version "0.2.9"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@pkgr/core/-/core-0.2.9.tgz"
  integrity sha1-0imnt/nawWehVpku8jx/AjZT9Ts=

"@protobufjs/aspromise@^1.1.1", "@protobufjs/aspromise@^1.1.2":
  version "1.1.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@protobufjs/aspromise/-/aspromise-1.1.2.tgz"
  integrity sha1-m4sMxmPWaafY9vXQiToU00jzD78=

"@protobufjs/base64@^1.1.2":
  version "1.1.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@protobufjs/base64/-/base64-1.1.2.tgz"
  integrity sha1-TIVzDlm5ofHzSQR9vyQpYDS7JzU=

"@protobufjs/codegen@^2.0.4":
  version "2.0.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@protobufjs/codegen/-/codegen-2.0.4.tgz"
  integrity sha1-fvN/DQEPsCitGtWXIuUG2SYoFcs=

"@protobufjs/eventemitter@^1.1.0":
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@protobufjs/eventemitter/-/eventemitter-1.1.0.tgz"
  integrity sha1-NVy8mLr61ZePntCV85diHx0Ga3A=

"@protobufjs/fetch@^1.1.0":
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@protobufjs/fetch/-/fetch-1.1.0.tgz"
  integrity sha1-upn7WYYUr2VwDBYZ/wbUVLDYTEU=
  dependencies:
    "@protobufjs/aspromise" "^1.1.1"
    "@protobufjs/inquire" "^1.1.0"

"@protobufjs/float@^1.0.2":
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@protobufjs/float/-/float-1.0.2.tgz"
  integrity sha1-Xp4avctz/Ap8uLKR33jIy9l7h9E=

"@protobufjs/inquire@^1.1.0":
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@protobufjs/inquire/-/inquire-1.1.0.tgz"
  integrity sha1-/yAOPnzyQp4tyvwRQIKOjMY48Ik=

"@protobufjs/path@^1.1.2":
  version "1.1.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@protobufjs/path/-/path-1.1.2.tgz"
  integrity sha1-bMKyDFya1q0NzP0hynZz2Nf79o0=

"@protobufjs/pool@^1.1.0":
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@protobufjs/pool/-/pool-1.1.0.tgz"
  integrity sha1-Cf0V8tbTq/qbZbw2ZQbWrXhG/1Q=

"@protobufjs/utf8@^1.1.0":
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@protobufjs/utf8/-/utf8-1.1.0.tgz"
  integrity sha1-p3c2C1s5oaLlEG+OhY8v0tBgxXA=

"@redis/bloom@1.2.0":
  version "1.2.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@redis/bloom/-/bloom-1.2.0.tgz"
  integrity sha1-0/1tPArz75LyZ2e1ZBSjcMe2O3E=

"@redis/bloom@5.8.0":
  version "5.8.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@redis/bloom/-/bloom-5.8.0.tgz"
  integrity sha1-v9kQ9MU2GpjJMiZzHUIDWMCetmA=

"@redis/client@1.6.1":
  version "1.6.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@redis/client/-/client-1.6.1.tgz"
  integrity sha1-xGNrfLNOlgCKmIQJt+eHNkrnYaI=
  dependencies:
    cluster-key-slot "1.1.2"
    generic-pool "3.9.0"
    yallist "4.0.0"

"@redis/client@5.8.0":
  version "5.8.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@redis/client/-/client-5.8.0.tgz"
  integrity sha1-FpY9khH6rBPGLn2dvxZy3sW6bU0=
  dependencies:
    cluster-key-slot "1.1.2"

"@redis/graph@1.1.1":
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@redis/graph/-/graph-1.1.1.tgz"
  integrity sha1-jBDfLff30CdBhmdRdkAxqVehcOo=

"@redis/json@1.0.7":
  version "1.0.7"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@redis/json/-/json-1.0.7.tgz"
  integrity sha1-AWJX/NkzxMvLnEnN6KCWE3XGiTs=

"@redis/json@5.8.0":
  version "5.8.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@redis/json/-/json-5.8.0.tgz"
  integrity sha1-gCuzKAq2YuqSFUM2adXEm20OfJI=

"@redis/search@1.2.0":
  version "1.2.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@redis/search/-/search-1.2.0.tgz"
  integrity sha1-UJdv0/MRaPWFZm95It3hEcdFZ7g=

"@redis/search@5.8.0":
  version "5.8.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@redis/search/-/search-5.8.0.tgz"
  integrity sha1-VNmWofbxTzXVXiteqlfmuipREAM=

"@redis/time-series@1.1.0":
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@redis/time-series/-/time-series-1.1.0.tgz"
  integrity sha1-y6RUwF7CAb1VR6r1UobURoKsjrU=

"@redis/time-series@5.8.0":
  version "5.8.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@redis/time-series/-/time-series-5.8.0.tgz"
  integrity sha1-fcTywIwEgrrB+EXY6UnEZeQv5g8=

"@scarf/scarf@=1.4.0":
  version "1.4.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@scarf/scarf/-/scarf-1.4.0.tgz"
  integrity sha1-O7uYQIXb1tmCSUU4tSO+HOZWKXI=

"@sinclair/typebox@^0.27.8":
  version "0.27.8"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@sinclair/typebox/-/typebox-0.27.8.tgz"
  integrity sha1-Zmf6wWxDa1Q0o4ejTe2wExmPbm4=

"@sinonjs/commons@^3.0.0":
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@sinonjs/commons/-/commons-3.0.1.tgz"
  integrity sha1-ECk1fkTKkBphVYX20nc428iQhM0=
  dependencies:
    type-detect "4.0.8"

"@sinonjs/fake-timers@^10.0.2":
  version "10.3.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@sinonjs/fake-timers/-/fake-timers-10.3.0.tgz"
  integrity sha1-Vf3/Hsq581QBkSna9N8N1Nkj6mY=
  dependencies:
    "@sinonjs/commons" "^3.0.0"

"@sqltools/formatter@^1.2.5":
  version "1.2.5"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@sqltools/formatter/-/formatter-1.2.5.tgz"
  integrity sha1-OrwgPHm4w+kP1sFWoMYtVANSDhI=

"@tediousjs/connection-string@^0.5.0":
  version "0.5.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@tediousjs/connection-string/-/connection-string-0.5.0.tgz"
  integrity sha1-mz2FjAQKrGvfVYS/RTcM71tlIrQ=

"@tokenizer/inflate@^0.2.7":
  version "0.2.7"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@tokenizer/inflate/-/inflate-0.2.7.tgz"
  integrity sha1-Mt2d/Jq+RXyJs9m3YPwGkMhaEDs=
  dependencies:
    debug "^4.4.0"
    fflate "^0.8.2"
    token-types "^6.0.0"

"@tokenizer/token@^0.3.0":
  version "0.3.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@tokenizer/token/-/token-0.3.0.tgz"
  integrity sha1-/pipP+eJJH6ZjHXnTpx8YyF6onY=

"@tsconfig/node10@^1.0.7":
  version "1.0.11"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@tsconfig/node10/-/node10-1.0.11.tgz"
  integrity sha1-buRkAGhfEw4ngSjHs4t+Ax/1svI=

"@tsconfig/node12@^1.0.7":
  version "1.0.11"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@tsconfig/node12/-/node12-1.0.11.tgz"
  integrity sha1-7j3vHyfZ7WbaxuRqKVz/sBUuBY0=

"@tsconfig/node14@^1.0.0":
  version "1.0.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@tsconfig/node14/-/node14-1.0.3.tgz"
  integrity sha1-5DhjFihPALmENb9A9y91oJ2r9sE=

"@tsconfig/node16@^1.0.2":
  version "1.0.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@tsconfig/node16/-/node16-1.0.4.tgz"
  integrity sha1-C5LcwMwcgfbzBqOB8o4xsaVlNuk=

"@types/babel__core@^7.1.14":
  version "7.20.5"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/babel__core/-/babel__core-7.20.5.tgz"
  integrity sha1-PfFfJ7qFMZyqB7oI0HIYibs5wBc=
  dependencies:
    "@babel/parser" "^7.20.7"
    "@babel/types" "^7.20.7"
    "@types/babel__generator" "*"
    "@types/babel__template" "*"
    "@types/babel__traverse" "*"

"@types/babel__generator@*":
  version "7.27.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/babel__generator/-/babel__generator-7.27.0.tgz"
  integrity sha1-tYGSlMUReZV6+uw0FEL5NB5BCKk=
  dependencies:
    "@babel/types" "^7.0.0"

"@types/babel__template@*":
  version "7.4.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/babel__template/-/babel__template-7.4.4.tgz"
  integrity sha1-VnJRNwHBshmbxtrWNqnXSRWGdm8=
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"

"@types/babel__traverse@*", "@types/babel__traverse@^7.0.6":
  version "7.28.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/babel__traverse/-/babel__traverse-7.28.0.tgz"
  integrity sha1-B9cT1szg0mXJhJ2wy+YtP2Hzb3Q=
  dependencies:
    "@babel/types" "^7.28.2"

"@types/body-parser@*":
  version "1.19.6"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/body-parser/-/body-parser-1.19.6.tgz"
  integrity sha1-GFm+u4/X2smRikXVTBlxq4ta9HQ=
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/compression@^1.8.1":
  version "1.8.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/compression/-/compression-1.8.1.tgz"
  integrity sha1-V80aXAxYWspWEkq02u8dJU1vWn0=
  dependencies:
    "@types/express" "*"
    "@types/node" "*"

"@types/connect@*":
  version "3.4.38"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/connect/-/connect-3.4.38.tgz"
  integrity sha1-W6fzvE+73q/43e2VLl/yzFP42Fg=
  dependencies:
    "@types/node" "*"

"@types/cookie-parser@^1.4.7":
  version "1.4.9"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/cookie-parser/-/cookie-parser-1.4.9.tgz"
  integrity sha1-8OecdmpY7nNppS51CbOEAiL2jtI=

"@types/cookiejar@^2.1.5":
  version "2.1.5"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/cookiejar/-/cookiejar-2.1.5.tgz"
  integrity sha1-FKPoP6ZBvrFpot2EItkcPDRamng=

"@types/eslint-scope@^3.7.7":
  version "3.7.7"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/eslint-scope/-/eslint-scope-3.7.7.tgz"
  integrity sha1-MQi9XxiwzbJ3yGez3UScntcHmsU=
  dependencies:
    "@types/eslint" "*"
    "@types/estree" "*"

"@types/eslint@*":
  version "9.6.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/eslint/-/eslint-9.6.1.tgz"
  integrity sha1-1Xla1zLOgXFfJ/ddqRMASlZ1FYQ=
  dependencies:
    "@types/estree" "*"
    "@types/json-schema" "*"

"@types/estree@*", "@types/estree@^1.0.6":
  version "1.0.8"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/estree/-/estree-1.0.8.tgz"
  integrity sha1-lYuRyZGxhnztMYvt6g4hXuBQcm4=

"@types/express-serve-static-core@^4.17.33":
  version "4.19.6"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/express-serve-static-core/-/express-serve-static-core-4.19.6.tgz"
  integrity sha1-4BMkwqAk/zZ9ksZvSFU87Qq1Amc=
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"
    "@types/send" "*"

"@types/express-session@^1.18.0":
  version "1.18.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/express-session/-/express-session-1.18.2.tgz"
  integrity sha1-d43DKW2pqpfVv45CNYpUxSojAxc=
  dependencies:
    "@types/express" "*"

"@types/express@*", "@types/express@^4.17.17":
  version "4.17.23"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/express/-/express-4.17.23.tgz"
  integrity sha1-Na8xk8ZAv9TX/ncZHNDtQRpDO+8=
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "^4.17.33"
    "@types/qs" "*"
    "@types/serve-static" "*"

"@types/graceful-fs@^4.1.3":
  version "4.1.9"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/graceful-fs/-/graceful-fs-4.1.9.tgz"
  integrity sha1-Kga8D2iiCrN7PjaqI4vmq99J6LQ=
  dependencies:
    "@types/node" "*"

"@types/helmet@^4.0.0":
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/helmet/-/helmet-4.0.0.tgz"
  integrity sha1-r3r0beJqvjaLhTYHaa6ZOL+yMYo=
  dependencies:
    helmet "*"

"@types/http-errors@*":
  version "2.0.5"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/http-errors/-/http-errors-2.0.5.tgz"
  integrity sha1-W3SasrFroRNCP+saZKldzTA5hHI=

"@types/istanbul-lib-coverage@*", "@types/istanbul-lib-coverage@^2.0.0", "@types/istanbul-lib-coverage@^2.0.1":
  version "2.0.6"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.6.tgz"
  integrity sha1-dznCMqH+6bTTzomF8xTAxtM1Sdc=

"@types/istanbul-lib-report@*":
  version "3.0.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.3.tgz"
  integrity sha1-UwR2FK5y4Z/AQB2HLeOuK0zjUL8=
  dependencies:
    "@types/istanbul-lib-coverage" "*"

"@types/istanbul-reports@^3.0.0":
  version "3.0.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/istanbul-reports/-/istanbul-reports-3.0.4.tgz"
  integrity sha1-DwPj0vZw+9rFhuNLQzeDBwzBb1Q=
  dependencies:
    "@types/istanbul-lib-report" "*"

"@types/jest@^29.5.2":
  version "29.5.14"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/jest/-/jest-29.5.14.tgz"
  integrity sha1-K5EJEvodaFbK3NDB+Vr33x1gSeU=
  dependencies:
    expect "^29.0.0"
    pretty-format "^29.0.0"

"@types/json-schema@*", "@types/json-schema@^7.0.8", "@types/json-schema@^7.0.9":
  version "7.0.15"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/json-schema/-/json-schema-7.0.15.tgz"
  integrity sha1-WWoXRyM2lNUPatinhp/Lb1bPWEE=

"@types/jsonwebtoken@9.0.5":
  version "9.0.5"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/jsonwebtoken/-/jsonwebtoken-9.0.5.tgz"
  integrity sha1-C9m4Qcnmxak3wXZW4jaPZdoCVYg=
  dependencies:
    "@types/node" "*"

"@types/lodash@^4.17.16":
  version "4.17.20"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/lodash/-/lodash-4.17.20.tgz"
  integrity sha1-HKdzYdc2NDLSn15VlQ2eweHG6pM=

"@types/methods@^1.1.4":
  version "1.1.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/methods/-/methods-1.1.4.tgz"
  integrity sha1-07esMKxHyRBU6pUc6e7QexBR5Uc=

"@types/mime@^1":
  version "1.3.5"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/mime/-/mime-1.3.5.tgz"
  integrity sha1-HvMC4Bz30rWg+lJnkMkSO/HQZpA=

"@types/multer@^1.4.12":
  version "1.4.13"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/multer/-/multer-1.4.13.tgz"
  integrity sha1-vkg/kJp38T4GJMrD0AGFnrEq5os=
  dependencies:
    "@types/express" "*"

"@types/node@*", "@types/node@^20.3.1", "@types/node@>=13.7.0", "@types/node@>=18":
  version "20.19.9"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/node/-/node-20.19.9.tgz"
  integrity sha1-yppYGT/sNhzG6FnYi1ImGFPx8NM=
  dependencies:
    undici-types "~6.21.0"

"@types/qs@*":
  version "6.14.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/qs/-/qs-6.14.0.tgz"
  integrity sha1-2LYM7PYvLbD7aOXgBgd7kXi4XeU=

"@types/range-parser@*":
  version "1.2.7"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/range-parser/-/range-parser-1.2.7.tgz"
  integrity sha1-UK5DU+qt3AQEQnmBL1LIxlhX28s=

"@types/readable-stream@^4.0.0":
  version "4.0.21"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/readable-stream/-/readable-stream-4.0.21.tgz"
  integrity sha1-cWVYRUpeDDwGUVIPgVTvwyiPWcs=
  dependencies:
    "@types/node" "*"

"@types/send@*":
  version "0.17.5"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/send/-/send-0.17.5.tgz"
  integrity sha1-2ZHU8rFvKx70lxMfAKkRQpB5HnQ=
  dependencies:
    "@types/mime" "^1"
    "@types/node" "*"

"@types/serve-static@*":
  version "1.15.8"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/serve-static/-/serve-static-1.15.8.tgz"
  integrity sha1-gYDD++SnDo8AufcLm6fwjzWYeHc=
  dependencies:
    "@types/http-errors" "*"
    "@types/node" "*"
    "@types/send" "*"

"@types/stack-utils@^2.0.0":
  version "2.0.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/stack-utils/-/stack-utils-2.0.3.tgz"
  integrity sha1-YgkyHrLBcSp+dGZCK4yx/A2d1dg=

"@types/superagent@^8.1.0":
  version "8.1.9"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/superagent/-/superagent-8.1.9.tgz"
  integrity sha1-KL/kZY5GmDjtC/ZtiYNUvKsh9J8=
  dependencies:
    "@types/cookiejar" "^2.1.5"
    "@types/methods" "^1.1.4"
    "@types/node" "*"
    form-data "^4.0.0"

"@types/supertest@^6.0.0":
  version "6.0.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/supertest/-/supertest-6.0.3.tgz"
  integrity sha1-1zbw6ZSxlbY+HJPoAnGi+vknOIw=
  dependencies:
    "@types/methods" "^1.1.4"
    "@types/superagent" "^8.1.0"

"@types/validator@^13.11.8":
  version "13.15.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/validator/-/validator-13.15.2.tgz"
  integrity sha1-7lM6IKuXffNpF6RUdUx+DfSqb48=

"@types/yargs-parser@*":
  version "21.0.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/yargs-parser/-/yargs-parser-21.0.3.tgz"
  integrity sha1-gV4wt4bS6PDc2F/VvPXhoE0AjxU=

"@types/yargs@^17.0.8":
  version "17.0.33"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/yargs/-/yargs-17.0.33.tgz"
  integrity sha1-jDIwPag+7AUKhLPHrnufki0T4y0=
  dependencies:
    "@types/yargs-parser" "*"

"@typescript-eslint/eslint-plugin@^7.0.0":
  version "7.18.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@typescript-eslint/eslint-plugin/-/eslint-plugin-7.18.0.tgz"
  integrity sha1-sW088+52v1cv31EeecJIvexhnqM=
  dependencies:
    "@eslint-community/regexpp" "^4.10.0"
    "@typescript-eslint/scope-manager" "7.18.0"
    "@typescript-eslint/type-utils" "7.18.0"
    "@typescript-eslint/utils" "7.18.0"
    "@typescript-eslint/visitor-keys" "7.18.0"
    graphemer "^1.4.0"
    ignore "^5.3.1"
    natural-compare "^1.4.0"
    ts-api-utils "^1.3.0"

"@typescript-eslint/parser@^7.0.0":
  version "7.18.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@typescript-eslint/parser/-/parser-7.18.0.tgz"
  integrity sha1-g5KNDxt/SvqXQJjGS1zm+QUflqA=
  dependencies:
    "@typescript-eslint/scope-manager" "7.18.0"
    "@typescript-eslint/types" "7.18.0"
    "@typescript-eslint/typescript-estree" "7.18.0"
    "@typescript-eslint/visitor-keys" "7.18.0"
    debug "^4.3.4"

"@typescript-eslint/scope-manager@7.18.0":
  version "7.18.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@typescript-eslint/scope-manager/-/scope-manager-7.18.0.tgz"
  integrity sha1-ySjnqfwsCz7ZKrMRLGFNa9mVHIM=
  dependencies:
    "@typescript-eslint/types" "7.18.0"
    "@typescript-eslint/visitor-keys" "7.18.0"

"@typescript-eslint/type-utils@7.18.0":
  version "7.18.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@typescript-eslint/type-utils/-/type-utils-7.18.0.tgz"
  integrity sha1-IWX/ruALH7vdLUCqhSMtq2mY9Ts=
  dependencies:
    "@typescript-eslint/typescript-estree" "7.18.0"
    "@typescript-eslint/utils" "7.18.0"
    debug "^4.3.4"
    ts-api-utils "^1.3.0"

"@typescript-eslint/types@7.18.0":
  version "7.18.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@typescript-eslint/types/-/types-7.18.0.tgz"
  integrity sha1-uQpXzN6nF5f//6AyHnRPN57IOMk=

"@typescript-eslint/typescript-estree@7.18.0":
  version "7.18.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@typescript-eslint/typescript-estree/-/typescript-estree-7.18.0.tgz"
  integrity sha1-tYaNSGxRzo8xIwm6eb258zGzeTE=
  dependencies:
    "@typescript-eslint/types" "7.18.0"
    "@typescript-eslint/visitor-keys" "7.18.0"
    debug "^4.3.4"
    globby "^11.1.0"
    is-glob "^4.0.3"
    minimatch "^9.0.4"
    semver "^7.6.0"
    ts-api-utils "^1.3.0"

"@typescript-eslint/utils@7.18.0":
  version "7.18.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@typescript-eslint/utils/-/utils-7.18.0.tgz"
  integrity sha1-vKAc3nf5X8ao1bDby/s9bKS+RR8=
  dependencies:
    "@eslint-community/eslint-utils" "^4.4.0"
    "@typescript-eslint/scope-manager" "7.18.0"
    "@typescript-eslint/types" "7.18.0"
    "@typescript-eslint/typescript-estree" "7.18.0"

"@typescript-eslint/visitor-keys@7.18.0":
  version "7.18.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@typescript-eslint/visitor-keys/-/visitor-keys-7.18.0.tgz"
  integrity sha1-BWRim2Ek1nYHN40PAzKgSVsl59c=
  dependencies:
    "@typescript-eslint/types" "7.18.0"
    eslint-visitor-keys "^3.4.3"

"@typespec/ts-http-runtime@^0.3.0":
  version "0.3.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@typespec/ts-http-runtime/-/ts-http-runtime-0.3.0.tgz"
  integrity sha1-9Qb/IXDllKJX+OeKoZYIjzpGoi0=
  dependencies:
    http-proxy-agent "^7.0.0"
    https-proxy-agent "^7.0.0"
    tslib "^2.6.2"

"@ungap/structured-clone@^1.2.0":
  version "1.3.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@ungap/structured-clone/-/structured-clone-1.3.0.tgz"
  integrity sha1-0Gu7OE689sUF/eHD0O1N3/4Kr/g=

"@webassemblyjs/ast@^1.14.1", "@webassemblyjs/ast@1.14.1":
  version "1.14.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@webassemblyjs/ast/-/ast-1.14.1.tgz"
  integrity sha1-qfagfysDyVyNOMRTah/ftSH/VbY=
  dependencies:
    "@webassemblyjs/helper-numbers" "1.13.2"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"

"@webassemblyjs/floating-point-hex-parser@1.13.2":
  version "1.13.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@webassemblyjs/floating-point-hex-parser/-/floating-point-hex-parser-1.13.2.tgz"
  integrity sha1-/Moe7dscxOe27tT8eVbWgTshufs=

"@webassemblyjs/helper-api-error@1.13.2":
  version "1.13.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@webassemblyjs/helper-api-error/-/helper-api-error-1.13.2.tgz"
  integrity sha1-4KFhUiSLw42u523X4h8Vxe86sec=

"@webassemblyjs/helper-buffer@1.14.1":
  version "1.14.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@webassemblyjs/helper-buffer/-/helper-buffer-1.14.1.tgz"
  integrity sha1-giqbxgMWZTH31d+E5ntb+ZtyuWs=

"@webassemblyjs/helper-numbers@1.13.2":
  version "1.13.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@webassemblyjs/helper-numbers/-/helper-numbers-1.13.2.tgz"
  integrity sha1-29kyVI5xGfS4p4d/1ajSDmNJCy0=
  dependencies:
    "@webassemblyjs/floating-point-hex-parser" "1.13.2"
    "@webassemblyjs/helper-api-error" "1.13.2"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/helper-wasm-bytecode@1.13.2":
  version "1.13.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@webassemblyjs/helper-wasm-bytecode/-/helper-wasm-bytecode-1.13.2.tgz"
  integrity sha1-5VYQh1j0SKroTIUOWTzhig6zHgs=

"@webassemblyjs/helper-wasm-section@1.14.1":
  version "1.14.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.14.1.tgz"
  integrity sha1-lindqcRDDqtUtZEFPW3G87oFA0g=
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-buffer" "1.14.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"
    "@webassemblyjs/wasm-gen" "1.14.1"

"@webassemblyjs/ieee754@1.13.2":
  version "1.13.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@webassemblyjs/ieee754/-/ieee754-1.13.2.tgz"
  integrity sha1-HF6qzh1gatosf9cEXqk1bFnuDbo=
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.13.2":
  version "1.13.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@webassemblyjs/leb128/-/leb128-1.13.2.tgz"
  integrity sha1-V8XD3rAQXQLOJfo/109OvJ/Qu7A=
  dependencies:
    "@xtuc/long" "4.2.2"

"@webassemblyjs/utf8@1.13.2":
  version "1.13.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@webassemblyjs/utf8/-/utf8-1.13.2.tgz"
  integrity sha1-kXog6T9xrVYClmwtaFrgxsIfYPE=

"@webassemblyjs/wasm-edit@^1.14.1":
  version "1.14.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@webassemblyjs/wasm-edit/-/wasm-edit-1.14.1.tgz"
  integrity sha1-rGaJ9QIhm1kZjd7ELc1JaxAE1Zc=
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-buffer" "1.14.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"
    "@webassemblyjs/helper-wasm-section" "1.14.1"
    "@webassemblyjs/wasm-gen" "1.14.1"
    "@webassemblyjs/wasm-opt" "1.14.1"
    "@webassemblyjs/wasm-parser" "1.14.1"
    "@webassemblyjs/wast-printer" "1.14.1"

"@webassemblyjs/wasm-gen@1.14.1":
  version "1.14.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@webassemblyjs/wasm-gen/-/wasm-gen-1.14.1.tgz"
  integrity sha1-mR5/DAkMsLtiu6yIIHbj0hnalXA=
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"
    "@webassemblyjs/ieee754" "1.13.2"
    "@webassemblyjs/leb128" "1.13.2"
    "@webassemblyjs/utf8" "1.13.2"

"@webassemblyjs/wasm-opt@1.14.1":
  version "1.14.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@webassemblyjs/wasm-opt/-/wasm-opt-1.14.1.tgz"
  integrity sha1-5vce18yuRngcIGAX08FMUO+oEGs=
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-buffer" "1.14.1"
    "@webassemblyjs/wasm-gen" "1.14.1"
    "@webassemblyjs/wasm-parser" "1.14.1"

"@webassemblyjs/wasm-parser@^1.14.1", "@webassemblyjs/wasm-parser@1.14.1":
  version "1.14.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@webassemblyjs/wasm-parser/-/wasm-parser-1.14.1.tgz"
  integrity sha1-s+E/GJNgXKeLUsaOVM9qhl+Qufs=
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-api-error" "1.13.2"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"
    "@webassemblyjs/ieee754" "1.13.2"
    "@webassemblyjs/leb128" "1.13.2"
    "@webassemblyjs/utf8" "1.13.2"

"@webassemblyjs/wast-printer@1.14.1":
  version "1.14.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@webassemblyjs/wast-printer/-/wast-printer-1.14.1.tgz"
  integrity sha1-O7PpY4qK5f2vlhDnoGtNn5qm/gc=
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@xtuc/long" "4.2.2"

"@xtuc/ieee754@^1.2.0":
  version "1.2.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@xtuc/ieee754/-/ieee754-1.2.0.tgz"
  integrity sha1-7vAUoxRa5Hehy8AM0eVSM23Ot5A=

"@xtuc/long@4.2.2":
  version "4.2.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@xtuc/long/-/long-4.2.2.tgz"
  integrity sha1-0pHGpOl5ibXGHZrPOWrk/hM6cY0=

abort-controller@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/abort-controller/-/abort-controller-3.0.0.tgz"
  integrity sha1-6vVNU7YrrkE46AnKIlyEOabvs5I=
  dependencies:
    event-target-shim "^5.0.0"

accepts@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/accepts/-/accepts-2.0.0.tgz"
  integrity sha1-u89LpQdUZ/PyEx6rPP/HPC9deJU=
  dependencies:
    mime-types "^3.0.0"
    negotiator "^1.0.0"

acorn-import-attributes@^1.9.5:
  version "1.9.5"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/acorn-import-attributes/-/acorn-import-attributes-1.9.5.tgz"
  integrity sha1-frFVexugXvGLXtDsZ1kb+rBGiO8=

acorn-jsx@^5.3.2:
  version "5.3.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/acorn-jsx/-/acorn-jsx-5.3.2.tgz"
  integrity sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=

acorn-walk@^8.1.1:
  version "8.3.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/acorn-walk/-/acorn-walk-8.3.4.tgz"
  integrity sha1-eU3RacOXft9LpOpHWDWHxYZiNrc=
  dependencies:
    acorn "^8.11.0"

acorn@^8.11.0, acorn@^8.14.0, acorn@^8.4.1, acorn@^8.9.0:
  version "8.15.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/acorn/-/acorn-8.15.0.tgz"
  integrity sha1-o2CJi8QV7arEbIJB9jg5dbkwuBY=

agent-base@^7.1.0, agent-base@^7.1.2:
  version "7.1.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/agent-base/-/agent-base-7.1.4.tgz"
  integrity sha1-48121MVI7oldPD/Y3B9sW5Ay56g=

ajv-formats@^2.1.1, ajv-formats@2.1.1:
  version "2.1.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ajv-formats/-/ajv-formats-2.1.1.tgz"
  integrity sha1-bmaUAGWet0lzu/LjMycYCgmWtSA=
  dependencies:
    ajv "^8.0.0"

ajv-keywords@^3.5.2:
  version "3.5.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ajv-keywords/-/ajv-keywords-3.5.2.tgz"
  integrity sha1-MfKdpatuANHC0yms97WSlhTVAU0=

ajv-keywords@^5.1.0:
  version "5.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ajv-keywords/-/ajv-keywords-5.1.0.tgz"
  integrity sha1-adTThaRzPNvqtElkoRcKiPh/DhY=
  dependencies:
    fast-deep-equal "^3.1.3"

ajv@^6.12.4:
  version "6.12.6"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ajv/-/ajv-6.12.6.tgz"
  integrity sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ajv@^6.12.5:
  version "6.12.6"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ajv/-/ajv-6.12.6.tgz"
  integrity sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ajv@^8.0.0, ajv@^8.9.0, ajv@8.12.0:
  version "8.12.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ajv/-/ajv-8.12.0.tgz"
  integrity sha1-0aBScyPiL1NWLFZ8AJkVd9++GdE=
  dependencies:
    fast-deep-equal "^3.1.1"
    json-schema-traverse "^1.0.0"
    require-from-string "^2.0.2"
    uri-js "^4.2.2"

ansi-colors@4.1.3:
  version "4.1.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ansi-colors/-/ansi-colors-4.1.3.tgz"
  integrity sha1-N2ETQOsiQ+cMxgTK011jJw1IeBs=

ansi-escapes@^4.2.1, ansi-escapes@^4.3.2:
  version "4.3.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ansi-escapes/-/ansi-escapes-4.3.2.tgz"
  integrity sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4=
  dependencies:
    type-fest "^0.21.3"

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ansi-regex/-/ansi-regex-5.0.1.tgz"
  integrity sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=

ansi-regex@^6.0.1:
  version "6.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ansi-regex/-/ansi-regex-6.1.0.tgz"
  integrity sha1-lexAnGlhnWyxuLNPFLZg7yjr1lQ=

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ansi-styles/-/ansi-styles-4.3.0.tgz"
  integrity sha1-7dgDYornHATIWuegkG7a00tkiTc=
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^5.0.0:
  version "5.2.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ansi-styles/-/ansi-styles-5.2.0.tgz"
  integrity sha1-B0SWkK1Fd30ZJKwquy/IiV26g2s=

ansi-styles@^6.1.0:
  version "6.2.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ansi-styles/-/ansi-styles-6.2.1.tgz"
  integrity sha1-DmIyDPmcIa//OzASGSVGqsv7BcU=

ansis@^3.17.0:
  version "3.17.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ansis/-/ansis-3.17.0.tgz"
  integrity sha1-+o2cKpP+fRF34MF/nutWKlioMtc=

anymatch@^3.0.3, anymatch@~3.1.2:
  version "3.1.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/anymatch/-/anymatch-3.1.3.tgz"
  integrity sha1-eQxYsZuhcgqEIFtXxhjVrYUklz4=
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

app-root-path@^3.1.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/app-root-path/-/app-root-path-3.1.0.tgz"
  integrity sha1-WXGi/BK6FwNpp6HvAYxx5uR8LoY=

append-field@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/append-field/-/append-field-1.0.0.tgz"
  integrity sha1-HjRA6RXwsSA9I3SOeO3XubW0PlY=

arg@^4.1.0:
  version "4.1.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/arg/-/arg-4.1.3.tgz"
  integrity sha1-Jp/HrVuOQstjyJbVZmAXJhwUQIk=

argparse@^1.0.7:
  version "1.0.10"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/argparse/-/argparse-1.0.10.tgz"
  integrity sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=
  dependencies:
    sprintf-js "~1.0.2"

argparse@^2.0.1:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/argparse/-/argparse-2.0.1.tgz"
  integrity sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=

array-timsort@^1.0.3:
  version "1.0.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/array-timsort/-/array-timsort-1.0.3.tgz"
  integrity sha1-PJ5BmeVPsrnD/ll2OWohYU7w2SY=

array-union@^2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/array-union/-/array-union-2.1.0.tgz"
  integrity sha1-t5hCCtvrHego2ErNii4j0+/oXo0=

asap@^2.0.0:
  version "2.0.6"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/asap/-/asap-2.0.6.tgz"
  integrity sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY=

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/asynckit/-/asynckit-0.4.0.tgz"
  integrity sha1-x57Zf380y48robyXkLzDZkdLS3k=

available-typed-arrays@^1.0.7:
  version "1.0.7"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/available-typed-arrays/-/available-typed-arrays-1.0.7.tgz"
  integrity sha1-pcw3XWoDwu/IelU/PgsVIt7xSEY=
  dependencies:
    possible-typed-array-names "^1.0.0"

babel-jest@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/babel-jest/-/babel-jest-29.7.0.tgz"
  integrity sha1-9DaZGSJbaExWCFmYrGPb0FvgINU=
  dependencies:
    "@jest/transform" "^29.7.0"
    "@types/babel__core" "^7.1.14"
    babel-plugin-istanbul "^6.1.1"
    babel-preset-jest "^29.6.3"
    chalk "^4.0.0"
    graceful-fs "^4.2.9"
    slash "^3.0.0"

babel-plugin-istanbul@^6.1.1:
  version "6.1.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/babel-plugin-istanbul/-/babel-plugin-istanbul-6.1.1.tgz"
  integrity sha1-+ojsWSMv2bTjbbvFQKjsmptH2nM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@istanbuljs/load-nyc-config" "^1.0.0"
    "@istanbuljs/schema" "^0.1.2"
    istanbul-lib-instrument "^5.0.4"
    test-exclude "^6.0.0"

babel-plugin-jest-hoist@^29.6.3:
  version "29.6.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-29.6.3.tgz"
  integrity sha1-qtvpQ0ZBgqiSLDySfDBn/0DSRiY=
  dependencies:
    "@babel/template" "^7.3.3"
    "@babel/types" "^7.3.3"
    "@types/babel__core" "^7.1.14"
    "@types/babel__traverse" "^7.0.6"

babel-preset-current-node-syntax@^1.0.0:
  version "1.2.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-1.2.0.tgz"
  integrity sha1-IHMNbNx92l2JQByrEKxqMgZ6zeY=
  dependencies:
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-bigint" "^7.8.3"
    "@babel/plugin-syntax-class-properties" "^7.12.13"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"
    "@babel/plugin-syntax-import-attributes" "^7.24.7"
    "@babel/plugin-syntax-import-meta" "^7.10.4"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"
    "@babel/plugin-syntax-top-level-await" "^7.14.5"

babel-preset-jest@^29.6.3:
  version "29.6.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/babel-preset-jest/-/babel-preset-jest-29.6.3.tgz"
  integrity sha1-+gX6UQ59STiW17DdIDNgHIQPFxw=
  dependencies:
    babel-plugin-jest-hoist "^29.6.3"
    babel-preset-current-node-syntax "^1.0.0"

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/balanced-match/-/balanced-match-1.0.2.tgz"
  integrity sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=

base64-js@^1.3.1:
  version "1.5.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/base64-js/-/base64-js-1.5.1.tgz"
  integrity sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=

binary-extensions@^2.0.0:
  version "2.3.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/binary-extensions/-/binary-extensions-2.3.0.tgz"
  integrity sha1-9uFKl4WNMnJSIAJC1Mz+UixEVSI=

bl@^4.1.0:
  version "4.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/bl/-/bl-4.1.0.tgz"
  integrity sha1-RRU1JkGCvsL7vIOmKrmM8R2fezo=
  dependencies:
    buffer "^5.5.0"
    inherits "^2.0.4"
    readable-stream "^3.4.0"

bl@^6.0.11:
  version "6.1.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/bl/-/bl-6.1.1.tgz"
  integrity sha1-kBjBrg+MxrBvkmETW/xnjVyGCLU=
  dependencies:
    "@types/readable-stream" "^4.0.0"
    buffer "^6.0.3"
    inherits "^2.0.4"
    readable-stream "^4.2.0"

body-parser@^1.20.3:
  version "1.20.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/body-parser/-/body-parser-1.20.3.tgz"
  integrity sha1-GVNDEiHG+1zWPEs21T+rCSjlSMY=
  dependencies:
    bytes "3.1.2"
    content-type "~1.0.5"
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    on-finished "2.4.1"
    qs "6.13.0"
    raw-body "2.5.2"
    type-is "~1.6.18"
    unpipe "1.0.0"

body-parser@^2.2.0:
  version "2.2.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/body-parser/-/body-parser-2.2.0.tgz"
  integrity sha1-96llbeMFJJpxW1Sbe4/Rq5393Po=
  dependencies:
    bytes "^3.1.2"
    content-type "^1.0.5"
    debug "^4.4.0"
    http-errors "^2.0.0"
    iconv-lite "^0.6.3"
    on-finished "^2.4.1"
    qs "^6.14.0"
    raw-body "^3.0.0"
    type-is "^2.0.0"

brace-expansion@^1.1.7:
  version "1.1.12"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/brace-expansion/-/brace-expansion-1.1.12.tgz"
  integrity sha1-q5tFRGblqMw6GHvqrVgEEqnFuEM=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

brace-expansion@^2.0.1:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/brace-expansion/-/brace-expansion-2.0.2.tgz"
  integrity sha1-VPxTI3phPYVMe9N0Y6rRffhyFOc=
  dependencies:
    balanced-match "^1.0.0"

braces@^3.0.3, braces@~3.0.2:
  version "3.0.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/braces/-/braces-3.0.3.tgz"
  integrity sha1-SQMy9AkZRSJy1VqEgK3AxEE1h4k=
  dependencies:
    fill-range "^7.1.1"

browserslist@^4.24.0:
  version "4.25.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/browserslist/-/browserslist-4.25.1.tgz"
  integrity sha1-up6ObymKHYb4Kcm5deB5SJZ7sRE=
  dependencies:
    caniuse-lite "^1.0.30001726"
    electron-to-chromium "^1.5.173"
    node-releases "^2.0.19"
    update-browserslist-db "^1.1.3"

bs-logger@^0.2.6:
  version "0.2.6"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/bs-logger/-/bs-logger-0.2.6.tgz"
  integrity sha1-6302UwenLPl0zGzadraDVK0za9g=
  dependencies:
    fast-json-stable-stringify "2.x"

bser@2.1.1:
  version "2.1.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/bser/-/bser-2.1.1.tgz"
  integrity sha1-5nh9og7OnQeZhTPP2d5vXDj0vAU=
  dependencies:
    node-int64 "^0.4.0"

buffer-equal-constant-time@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/buffer-equal-constant-time/-/buffer-equal-constant-time-1.0.1.tgz"
  integrity sha1-+OcRMvf/5uAaXJaXpMbz5I1cyBk=

buffer-from@^1.0.0:
  version "1.1.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/buffer-from/-/buffer-from-1.1.2.tgz"
  integrity sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=

buffer@^5.5.0:
  version "5.7.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/buffer/-/buffer-5.7.1.tgz"
  integrity sha1-umLnwTEzBTWCGXFghRqPZI6Z7tA=
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.1.13"

buffer@^6.0.3:
  version "6.0.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/buffer/-/buffer-6.0.3.tgz"
  integrity sha1-Ks5XhFnMj74qcKqo9S7mO2p0xsY=
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.2.1"

bundle-name@^4.1.0:
  version "4.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/bundle-name/-/bundle-name-4.1.0.tgz"
  integrity sha1-87lrNBYNZDGhnXaIE1r3z7h5eIk=
  dependencies:
    run-applescript "^7.0.0"

busboy@^1.6.0:
  version "1.6.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/busboy/-/busboy-1.6.0.tgz"
  integrity sha1-lm6japUC5DzbkUaWJSO5L1MfaJM=
  dependencies:
    streamsearch "^1.1.0"

bytes@^3.1.2, bytes@3.1.2:
  version "3.1.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/bytes/-/bytes-3.1.2.tgz"
  integrity sha1-iwvuuYYFrfGxKPpDhkA8AJ4CIaU=

call-bind-apply-helpers@^1.0.0, call-bind-apply-helpers@^1.0.1, call-bind-apply-helpers@^1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz"
  integrity sha1-S1QowiK+mF15w9gmV0edvgtZstY=
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"

call-bind@^1.0.8:
  version "1.0.8"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/call-bind/-/call-bind-1.0.8.tgz"
  integrity sha1-BzapZg9TfjOIgm9EDV7EX3ROqkw=
  dependencies:
    call-bind-apply-helpers "^1.0.0"
    es-define-property "^1.0.0"
    get-intrinsic "^1.2.4"
    set-function-length "^1.2.2"

call-bound@^1.0.2, call-bound@^1.0.3, call-bound@^1.0.4:
  version "1.0.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/call-bound/-/call-bound-1.0.4.tgz"
  integrity sha1-I43pNdKippKSjFOMfM+pEGf9Bio=
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    get-intrinsic "^1.3.0"

callsites@^3.0.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/callsites/-/callsites-3.1.0.tgz"
  integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=

camelcase@^5.3.1:
  version "5.3.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/camelcase/-/camelcase-5.3.1.tgz"
  integrity sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=

camelcase@^6.2.0:
  version "6.3.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/camelcase/-/camelcase-6.3.0.tgz"
  integrity sha1-VoW5XrIJrJwMF3Rnd4ychN9Yupo=

caniuse-lite@^1.0.30001726:
  version "1.0.30001733"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/caniuse-lite/-/caniuse-lite-1.0.30001733.tgz"
  integrity sha1-kYQF7WZHpihA+zKIMs9aA/mGl0s=

chalk@^4.0.0, chalk@^4.1.0, chalk@^4.1.1, chalk@^4.1.2, chalk@4.1.2:
  version "4.1.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/chalk/-/chalk-4.1.2.tgz"
  integrity sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chalk@^5.3.0:
  version "5.5.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/chalk/-/chalk-5.5.0.tgz"
  integrity sha1-Z62h31yoCdyEybgZ12QY3c8ShCg=

char-regex@^1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/char-regex/-/char-regex-1.0.2.tgz"
  integrity sha1-10Q1giYhf5ge1Y9Hmx1rzClUXc8=

chardet@^0.7.0:
  version "0.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/chardet/-/chardet-0.7.0.tgz"
  integrity sha1-kAlISfCTfy7twkJdDSip5fDLrZ4=

chokidar@^3.5.3, chokidar@3.6.0:
  version "3.6.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/chokidar/-/chokidar-3.6.0.tgz"
  integrity sha1-GXxsxmnvKo3F57TZfuTgksPrDVs=
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chrome-trace-event@^1.0.2:
  version "1.0.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/chrome-trace-event/-/chrome-trace-event-1.0.4.tgz"
  integrity sha1-Bb/9f/koRlCTMUcIyTvfqb0fD1s=

ci-info@^3.2.0:
  version "3.9.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ci-info/-/ci-info-3.9.0.tgz"
  integrity sha1-QnmmICinsfJi80c/yWBfXiGMWbQ=

cjs-module-lexer@^1.0.0, cjs-module-lexer@^1.2.2:
  version "1.4.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/cjs-module-lexer/-/cjs-module-lexer-1.4.3.tgz"
  integrity sha1-D3lzHrjP4exyrNQGbvrJ1hmRsA0=

class-transformer@^0.5.1:
  version "0.5.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/class-transformer/-/class-transformer-0.5.1.tgz"
  integrity sha1-JBR9Xf/Sps6pMKMlCmd63flqszY=

class-validator@^0.14.1:
  version "0.14.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/class-validator/-/class-validator-0.14.2.tgz"
  integrity sha1-o96V7dJrcD6JwVGiAj08EVAwNA0=
  dependencies:
    "@types/validator" "^13.11.8"
    libphonenumber-js "^1.11.1"
    validator "^13.9.0"

cli-cursor@^3.1.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/cli-cursor/-/cli-cursor-3.1.0.tgz"
  integrity sha1-JkMFp65JDR0Dvwybp8kl0XU68wc=
  dependencies:
    restore-cursor "^3.1.0"

cli-spinners@^2.5.0:
  version "2.9.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/cli-spinners/-/cli-spinners-2.9.2.tgz"
  integrity sha1-F3Oo9LnE1qwxVj31Oz/B15Ri/kE=

cli-table3@0.6.5:
  version "0.6.5"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/cli-table3/-/cli-table3-0.6.5.tgz"
  integrity sha1-ATuRNRdic5wWqVZ8IaBGMuRJvy8=
  dependencies:
    string-width "^4.2.0"
  optionalDependencies:
    "@colors/colors" "1.5.0"

cli-width@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/cli-width/-/cli-width-3.0.0.tgz"
  integrity sha1-ovSEN6LKqaIkNueUvwceyeYc7fY=

cli-width@^4.1.0:
  version "4.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/cli-width/-/cli-width-4.1.0.tgz"
  integrity sha1-QtqsQdPCVO84rYrAN2chMBc2kcU=

cliui@^8.0.1:
  version "8.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/cliui/-/cliui-8.0.1.tgz"
  integrity sha1-DASwddsCy/5g3I5s8vVIaxo2CKo=
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.1"
    wrap-ansi "^7.0.0"

clone@^1.0.2:
  version "1.0.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/clone/-/clone-1.0.4.tgz"
  integrity sha1-2jCcwmPfFZlMaIypAheco8fNfH4=

cluster-key-slot@1.1.2:
  version "1.1.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/cluster-key-slot/-/cluster-key-slot-1.1.2.tgz"
  integrity sha1-iN2qRpBuMDtd4w0xU7fZ/goMGaw=

co@^4.6.0:
  version "4.6.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/co/-/co-4.6.0.tgz"
  integrity sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=

collect-v8-coverage@^1.0.0:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/collect-v8-coverage/-/collect-v8-coverage-1.0.2.tgz"
  integrity sha1-wLKbzTO80HeaE0TCE2BR5q/T2ek=

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/color-convert/-/color-convert-2.0.1.tgz"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-name@~1.1.4:
  version "1.1.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/color-name/-/color-name-1.1.4.tgz"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

combined-stream@^1.0.8:
  version "1.0.8"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/combined-stream/-/combined-stream-1.0.8.tgz"
  integrity sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=
  dependencies:
    delayed-stream "~1.0.0"

commander@^11.0.0:
  version "11.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/commander/-/commander-11.1.0.tgz"
  integrity sha1-Yv3OdgBqaOXBqzMU3JLoAOuD2QY=

commander@^2.20.0:
  version "2.20.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/commander/-/commander-2.20.3.tgz"
  integrity sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=

commander@4.1.1:
  version "4.1.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/commander/-/commander-4.1.1.tgz"
  integrity sha1-n9YCvZNilOnp70aj9NaWQESxgGg=

comment-json@4.2.5:
  version "4.2.5"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/comment-json/-/comment-json-4.2.5.tgz"
  integrity sha1-SC4IX3WcJwS2C8b5f1W4wBvEHnA=
  dependencies:
    array-timsort "^1.0.3"
    core-util-is "^1.0.3"
    esprima "^4.0.1"
    has-own-prop "^2.0.0"
    repeat-string "^1.6.1"

component-emitter@^1.3.1:
  version "1.3.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/component-emitter/-/component-emitter-1.3.1.tgz"
  integrity sha1-7x1XlvfZPxNe5vtoQ0CyZAPJfRc=

compressible@~2.0.18:
  version "2.0.18"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/compressible/-/compressible-2.0.18.tgz"
  integrity sha1-r1PMprBw1MPAdQ+9dyhqbXzEb7o=
  dependencies:
    mime-db ">= 1.43.0 < 2"

compression@^1.8.1:
  version "1.8.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/compression/-/compression-1.8.1.tgz"
  integrity sha1-SkXZCawWUJGVqaKL2RCUiJwYDXk=
  dependencies:
    bytes "3.1.2"
    compressible "~2.0.18"
    debug "2.6.9"
    negotiator "~0.6.4"
    on-headers "~1.1.0"
    safe-buffer "5.2.1"
    vary "~1.1.2"

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/concat-map/-/concat-map-0.0.1.tgz"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

concat-stream@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/concat-stream/-/concat-stream-2.0.0.tgz"
  integrity sha1-QUz1r3kKSMYKub5FJ9VtXkETPLE=
  dependencies:
    buffer-from "^1.0.0"
    inherits "^2.0.3"
    readable-stream "^3.0.2"
    typedarray "^0.0.6"

connect-redis@^7.1.1:
  version "7.1.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/connect-redis/-/connect-redis-7.1.1.tgz"
  integrity sha1-t4+R6211Ca6egZuzYrlLpFkHKh0=

consola@^2.15.0:
  version "2.15.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/consola/-/consola-2.15.3.tgz"
  integrity sha1-LhH5jWpL5x/3LgvfB70j4Sy2FVA=

consola@^3.2.3:
  version "3.4.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/consola/-/consola-3.4.2.tgz"
  integrity sha1-WvEQFFOXu2ev2rdwE/3DTK5ZDqc=

content-disposition@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/content-disposition/-/content-disposition-1.0.0.tgz"
  integrity sha1-hEQmyzmPk0yu/LsXIgASa8fOrOI=
  dependencies:
    safe-buffer "5.2.1"

content-type@^1.0.5, content-type@~1.0.5:
  version "1.0.5"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/content-type/-/content-type-1.0.5.tgz"
  integrity sha1-i3cxYmVtHRCGeEyPI6VM5tc9eRg=

convert-source-map@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/convert-source-map/-/convert-source-map-2.0.0.tgz"
  integrity sha1-S1YPZJ/E6RjdCrdc9JYei8iC2Co=

cookie-parser@^1.4.7:
  version "1.4.7"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/cookie-parser/-/cookie-parser-1.4.7.tgz"
  integrity sha1-4hJWNd/XZoiP/pDWDChkBPoOeyY=
  dependencies:
    cookie "0.7.2"
    cookie-signature "1.0.6"

cookie-signature@^1.2.1:
  version "1.2.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/cookie-signature/-/cookie-signature-1.2.2.tgz"
  integrity sha1-V8f8PMKTrKuf7FTXPhVpDr5KF5M=

cookie-signature@1.0.6:
  version "1.0.6"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/cookie-signature/-/cookie-signature-1.0.6.tgz"
  integrity sha1-4wOogrNCzD7oylE6eZmXNNqzriw=

cookie-signature@1.0.7:
  version "1.0.7"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/cookie-signature/-/cookie-signature-1.0.7.tgz"
  integrity sha1-q13Xq3V8VOYPN+9lUPSBxCbRBFQ=

cookie@^0.7.1, cookie@0.7.2:
  version "0.7.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/cookie/-/cookie-0.7.2.tgz"
  integrity sha1-VWNpxHKiupEPKXmJG1JrNDYjftc=

cookiejar@^2.1.4:
  version "2.1.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/cookiejar/-/cookiejar-2.1.4.tgz"
  integrity sha1-7macH+os9C3DFYVGnRk/7w1ldxs=

core-util-is@^1.0.3:
  version "1.0.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/core-util-is/-/core-util-is-1.0.3.tgz"
  integrity sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U=

cors@2.8.5:
  version "2.8.5"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/cors/-/cors-2.8.5.tgz"
  integrity sha1-6sEdpRWS3Ya58G9uesKTs9+HXSk=
  dependencies:
    object-assign "^4"
    vary "^1"

cosmiconfig@^8.2.0:
  version "8.3.6"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/cosmiconfig/-/cosmiconfig-8.3.6.tgz"
  integrity sha1-Bgorhx1m26bIU46hEYuhrBb1+uM=
  dependencies:
    import-fresh "^3.3.0"
    js-yaml "^4.1.0"
    parse-json "^5.2.0"
    path-type "^4.0.0"

create-jest@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/create-jest/-/create-jest-29.7.0.tgz"
  integrity sha1-o1XFs8seGvAroXf+ev1/7uSaUyA=
  dependencies:
    "@jest/types" "^29.6.3"
    chalk "^4.0.0"
    exit "^0.1.2"
    graceful-fs "^4.2.9"
    jest-config "^29.7.0"
    jest-util "^29.7.0"
    prompts "^2.0.1"

create-require@^1.1.0:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/create-require/-/create-require-1.1.1.tgz"
  integrity sha1-wdfo8eX2z8n/ZfnNNS03NIdWwzM=

cross-spawn@^7.0.2, cross-spawn@^7.0.3, cross-spawn@^7.0.6:
  version "7.0.6"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/cross-spawn/-/cross-spawn-7.0.6.tgz"
  integrity sha1-ilj+ePANzXDDcEUXWd+/rwPo7p8=
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

crypto-randomuuid@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/crypto-randomuuid/-/crypto-randomuuid-1.0.0.tgz"
  integrity sha1-rPWD5eCF6GeuI+EH/3AnkCT56ec=

dayjs@^1.11.13:
  version "1.11.13"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/dayjs/-/dayjs-1.11.13.tgz"
  integrity sha1-kkMLATkFXD67YBUKoT6GCktaNmw=

dc-polyfill@^0.1.10:
  version "0.1.10"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/dc-polyfill/-/dc-polyfill-0.1.10.tgz"
  integrity sha1-byraGp5ElYfDY8qYz7ebRDz3S3A=

dd-trace@^5.27.0:
  version "5.62.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/dd-trace/-/dd-trace-5.62.0.tgz"
  integrity sha1-zRNXN6hjnr3a5vZ2uOAlimdkoho=
  dependencies:
    "@datadog/libdatadog" "0.7.0"
    "@datadog/native-appsec" "10.0.1"
    "@datadog/native-iast-taint-tracking" "4.0.0"
    "@datadog/native-metrics" "3.1.1"
    "@datadog/pprof" "5.9.0"
    "@datadog/sketches-js" "2.1.1"
    "@datadog/wasm-js-rewriter" "4.0.1"
    "@isaacs/ttlcache" "^1.4.1"
    "@opentelemetry/api" "1.8.0"
    "@opentelemetry/core" "^1.14.0"
    crypto-randomuuid "^1.0.0"
    dc-polyfill "^0.1.10"
    ignore "^7.0.5"
    import-in-the-middle "^1.14.2"
    istanbul-lib-coverage "^3.2.2"
    jest-docblock "^29.7.0"
    jsonpath-plus "^10.3.0"
    koalas "^1.0.2"
    limiter "^1.1.5"
    lodash.sortby "^4.7.0"
    lru-cache "^10.4.3"
    module-details-from-path "^1.0.4"
    mutexify "^1.4.0"
    opentracing ">=0.14.7"
    path-to-regexp "^0.1.12"
    pprof-format "^2.1.0"
    protobufjs "^7.5.3"
    retry "^0.13.1"
    rfdc "^1.4.1"
    semifies "^1.0.0"
    shell-quote "^1.8.2"
    source-map "^0.7.4"
    tlhunter-sorted-set "^0.1.0"
    ttl-set "^1.0.0"

debug@^4.1.0, debug@^4.1.1, debug@^4.3.1, debug@^4.3.2, debug@^4.3.3, debug@^4.3.4, debug@^4.3.5, debug@^4.3.7, debug@^4.4.0, debug@4:
  version "4.4.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/debug/-/debug-4.4.1.tgz"
  integrity sha1-5ai8bLxMbNPmQwiwaTo9T6VQGJs=
  dependencies:
    ms "^2.1.3"

debug@2.6.9:
  version "2.6.9"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/debug/-/debug-2.6.9.tgz"
  integrity sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=
  dependencies:
    ms "2.0.0"

dedent@^1.0.0, dedent@^1.6.0:
  version "1.6.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/dedent/-/dedent-1.6.0.tgz"
  integrity sha1-edUtY4mx/6Z9K871m6UYR6nVA7I=

deep-is@^0.1.3:
  version "0.1.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/deep-is/-/deep-is-0.1.4.tgz"
  integrity sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=

deepmerge@^4.2.2:
  version "4.3.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/deepmerge/-/deepmerge-4.3.1.tgz"
  integrity sha1-RLXyFHzTsA1LVhN2hZZvJv0l3Uo=

default-browser-id@^5.0.0:
  version "5.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/default-browser-id/-/default-browser-id-5.0.0.tgz"
  integrity sha1-odmL+WDBUILYo/pp6DFQzMzDryY=

default-browser@^5.2.1:
  version "5.2.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/default-browser/-/default-browser-5.2.1.tgz"
  integrity sha1-e3umEgT/PkJbVWhprm0+nZ8XEs8=
  dependencies:
    bundle-name "^4.1.0"
    default-browser-id "^5.0.0"

defaults@^1.0.3:
  version "1.0.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/defaults/-/defaults-1.0.4.tgz"
  integrity sha1-sLAgYsHiqmL/XZUo8PmLqpCXjXo=
  dependencies:
    clone "^1.0.2"

define-data-property@^1.1.4:
  version "1.1.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/define-data-property/-/define-data-property-1.1.4.tgz"
  integrity sha1-iU3BQbt9MGCuQ2b2oBB+aPvkjF4=
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    gopd "^1.0.1"

define-lazy-prop@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/define-lazy-prop/-/define-lazy-prop-3.0.0.tgz"
  integrity sha1-27Ga37dG1/xtc0oGty9KANAhJV8=

delay@^5.0.0:
  version "5.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/delay/-/delay-5.0.0.tgz"
  integrity sha1-E3BF7xuW5QcQYN1b5gv5M0Q2vR0=

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/delayed-stream/-/delayed-stream-1.0.0.tgz"
  integrity sha1-3zrhmayt+31ECqrgsp4icrJOxhk=

depd@^2.0.0, depd@~2.0.0, depd@2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/depd/-/depd-2.0.0.tgz"
  integrity sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8=

destroy@1.2.0:
  version "1.2.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/destroy/-/destroy-1.2.0.tgz"
  integrity sha1-SANzVQmti+VSk0xn32FPlOZvoBU=

detect-newline@^3.0.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/detect-newline/-/detect-newline-3.1.0.tgz"
  integrity sha1-V29d/GOuGhkv8ZLYrTr2MImRtlE=

dezalgo@^1.0.4:
  version "1.0.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/dezalgo/-/dezalgo-1.0.4.tgz"
  integrity sha1-dRI1JgRpCEwTIVffqFfzhtTDPYE=
  dependencies:
    asap "^2.0.0"
    wrappy "1"

diff-sequences@^29.6.3:
  version "29.6.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/diff-sequences/-/diff-sequences-29.6.3.tgz"
  integrity sha1-Ter4lNEUB8Ue/IQYAS+ecLhOqSE=

diff@^4.0.1:
  version "4.0.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/diff/-/diff-4.0.2.tgz"
  integrity sha1-YPOuy4nV+uUgwRqhnvwruYKq3n0=

dir-glob@^3.0.1:
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/dir-glob/-/dir-glob-3.0.1.tgz"
  integrity sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8=
  dependencies:
    path-type "^4.0.0"

doctrine@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/doctrine/-/doctrine-3.0.0.tgz"
  integrity sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=
  dependencies:
    esutils "^2.0.2"

dotenv-expand@10.0.0:
  version "10.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/dotenv-expand/-/dotenv-expand-10.0.0.tgz"
  integrity sha1-EmBdAPsK9tClkuZVhYV4QDLk7zc=

dotenv-expand@12.0.1:
  version "12.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/dotenv-expand/-/dotenv-expand-12.0.1.tgz"
  integrity sha1-RL36IEo2gQBonsNdc4V1X1mc7rE=
  dependencies:
    dotenv "^16.4.5"

dotenv@^16.4.5:
  version "16.6.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/dotenv/-/dotenv-16.6.1.tgz"
  integrity sha1-dz8OaVJ6gxXHKF1e5zxEWdIKgCA=

dotenv@^16.4.7:
  version "16.6.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/dotenv/-/dotenv-16.6.1.tgz"
  integrity sha1-dz8OaVJ6gxXHKF1e5zxEWdIKgCA=

dotenv@^17.2.1:
  version "17.2.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/dotenv/-/dotenv-17.2.1.tgz"
  integrity sha1-bzLhD68BSINRVTjckioPuHZdmzI=

dotenv@16.4.5:
  version "16.4.5"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/dotenv/-/dotenv-16.4.5.tgz"
  integrity sha1-zdOztgTLMn4oa0di4TUC9xfLCZ8=

dotenv@16.4.7:
  version "16.4.7"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/dotenv/-/dotenv-16.4.7.tgz"
  integrity sha1-DiDFuClQFAqpm+NgqKX1IzX1PCY=

dunder-proto@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/dunder-proto/-/dunder-proto-1.0.1.tgz"
  integrity sha1-165mfh3INIL4tw/Q9u78UNow9Yo=
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    es-errors "^1.3.0"
    gopd "^1.2.0"

eastasianwidth@^0.2.0:
  version "0.2.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/eastasianwidth/-/eastasianwidth-0.2.0.tgz"
  integrity sha1-aWzi7Aqg5uqTo5f/zySqeEDIJ8s=

ecdsa-sig-formatter@1.0.11:
  version "1.0.11"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.11.tgz"
  integrity sha1-rg8PothQRe8UqBfao86azQSJ5b8=
  dependencies:
    safe-buffer "^5.0.1"

ee-first@1.1.1:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ee-first/-/ee-first-1.1.1.tgz"
  integrity sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=

electron-to-chromium@^1.5.173:
  version "1.5.199"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/electron-to-chromium/-/electron-to-chromium-1.5.199.tgz"
  integrity sha1-TYvpx4NiwF8JXrc5LppU8fsU/To=

emittery@^0.13.1:
  version "0.13.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/emittery/-/emittery-0.13.1.tgz"
  integrity sha1-wEuMNFdJDghHrlH87Tr1LTOOPa0=

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/emoji-regex/-/emoji-regex-8.0.0.tgz"
  integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=

emoji-regex@^9.2.2:
  version "9.2.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/emoji-regex/-/emoji-regex-9.2.2.tgz"
  integrity sha1-hAyIA7DYBH9P8M+WMXazLU7z7XI=

encodeurl@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/encodeurl/-/encodeurl-2.0.0.tgz"
  integrity sha1-e46omAd9fkCdOsRUdOo46vCFelg=

enhanced-resolve@^5.0.0, enhanced-resolve@^5.17.1, enhanced-resolve@^5.7.0:
  version "5.18.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/enhanced-resolve/-/enhanced-resolve-5.18.3.tgz"
  integrity sha1-m19MXAdrh4fHj+VAOSznaoiFW0Q=
  dependencies:
    graceful-fs "^4.2.4"
    tapable "^2.2.0"

error-ex@^1.3.1:
  version "1.3.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/error-ex/-/error-ex-1.3.2.tgz"
  integrity sha1-tKxAZIEH/c3PriQvQovqihTU8b8=
  dependencies:
    is-arrayish "^0.2.1"

es-define-property@^1.0.0, es-define-property@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/es-define-property/-/es-define-property-1.0.1.tgz"
  integrity sha1-mD6y+aZyTpMD9hrd8BHHLgngsPo=

es-errors@^1.3.0:
  version "1.3.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/es-errors/-/es-errors-1.3.0.tgz"
  integrity sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8=

es-module-lexer@^1.2.1:
  version "1.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/es-module-lexer/-/es-module-lexer-1.7.0.tgz"
  integrity sha1-kVlgFWGICoXyc0VgqQmbLDHlNyo=

es-object-atoms@^1.0.0, es-object-atoms@^1.1.1:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/es-object-atoms/-/es-object-atoms-1.1.1.tgz"
  integrity sha1-HE8sSDcydZfOadLKGQp/3RcjOME=
  dependencies:
    es-errors "^1.3.0"

es-set-tostringtag@^2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz"
  integrity sha1-8x274MGDsAptJutjJcgQwP0YvU0=
  dependencies:
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"
    has-tostringtag "^1.0.2"
    hasown "^2.0.2"

escalade@^3.1.1, escalade@^3.2.0:
  version "3.2.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/escalade/-/escalade-3.2.0.tgz"
  integrity sha1-ARo/aYVroYnf+n3I/M6Z0qh5A+U=

escape-html@^1.0.3:
  version "1.0.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/escape-html/-/escape-html-1.0.3.tgz"
  integrity sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=

escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

escape-string-regexp@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz"
  integrity sha1-owME6Z2qMuI7L9IPUbq9B8/8o0Q=

escape-string-regexp@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz"
  integrity sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=

eslint-config-prettier@^9.0.0:
  version "9.1.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/eslint-config-prettier/-/eslint-config-prettier-9.1.2.tgz"
  integrity sha1-kN60+gJZWS33dLYA29HSJJp4zpE=

eslint-plugin-prettier@^5.0.0:
  version "5.5.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/eslint-plugin-prettier/-/eslint-plugin-prettier-5.5.4.tgz"
  integrity sha1-nWHE6hHeWvcE1O3xCMgsz6fy5hw=
  dependencies:
    prettier-linter-helpers "^1.0.0"
    synckit "^0.11.7"

eslint-scope@^7.2.2:
  version "7.2.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/eslint-scope/-/eslint-scope-7.2.2.tgz"
  integrity sha1-3rT5JWM5DzIAaJSvYqItuhxGQj8=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^5.2.0"

eslint-scope@5.1.1:
  version "5.1.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/eslint-scope/-/eslint-scope-5.1.1.tgz"
  integrity sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^4.1.1"

eslint-visitor-keys@^3.4.1, eslint-visitor-keys@^3.4.3:
  version "3.4.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz"
  integrity sha1-DNcv6FUOPC6uFWqWpN3c0cisWAA=

eslint@^8.42.0:
  version "8.57.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/eslint/-/eslint-8.57.1.tgz"
  integrity sha1-ffEJZUq6fju+XI6uUzxeRh08bKk=
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@eslint-community/regexpp" "^4.6.1"
    "@eslint/eslintrc" "^2.1.4"
    "@eslint/js" "8.57.1"
    "@humanwhocodes/config-array" "^0.13.0"
    "@humanwhocodes/module-importer" "^1.0.1"
    "@nodelib/fs.walk" "^1.2.8"
    "@ungap/structured-clone" "^1.2.0"
    ajv "^6.12.4"
    chalk "^4.0.0"
    cross-spawn "^7.0.2"
    debug "^4.3.2"
    doctrine "^3.0.0"
    escape-string-regexp "^4.0.0"
    eslint-scope "^7.2.2"
    eslint-visitor-keys "^3.4.3"
    espree "^9.6.1"
    esquery "^1.4.2"
    esutils "^2.0.2"
    fast-deep-equal "^3.1.3"
    file-entry-cache "^6.0.1"
    find-up "^5.0.0"
    glob-parent "^6.0.2"
    globals "^13.19.0"
    graphemer "^1.4.0"
    ignore "^5.2.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    is-path-inside "^3.0.3"
    js-yaml "^4.1.0"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.4.1"
    lodash.merge "^4.6.2"
    minimatch "^3.1.2"
    natural-compare "^1.4.0"
    optionator "^0.9.3"
    strip-ansi "^6.0.1"
    text-table "^0.2.0"

espree@^9.6.0, espree@^9.6.1:
  version "9.6.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/espree/-/espree-9.6.1.tgz"
  integrity sha1-oqF7jkNGkKVDLy+AGM5x0zGkjG8=
  dependencies:
    acorn "^8.9.0"
    acorn-jsx "^5.3.2"
    eslint-visitor-keys "^3.4.1"

esprima@^4.0.0, esprima@^4.0.1:
  version "4.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/esprima/-/esprima-4.0.1.tgz"
  integrity sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=

esquery@^1.4.2:
  version "1.6.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/esquery/-/esquery-1.6.0.tgz"
  integrity sha1-kUGSNPgE2FKoLc7sPhbNwiz52uc=
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/esrecurse/-/esrecurse-4.3.0.tgz"
  integrity sha1-eteWTWeauyi+5yzsY3WLHF0smSE=
  dependencies:
    estraverse "^5.2.0"

estraverse@^4.1.1:
  version "4.3.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/estraverse/-/estraverse-4.3.0.tgz"
  integrity sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=

estraverse@^5.1.0, estraverse@^5.2.0:
  version "5.3.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/estraverse/-/estraverse-5.3.0.tgz"
  integrity sha1-LupSkHAvJquP5TcDcP+GyWXSESM=

esutils@^2.0.2:
  version "2.0.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/esutils/-/esutils-2.0.3.tgz"
  integrity sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=

etag@^1.8.1:
  version "1.8.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/etag/-/etag-1.8.1.tgz"
  integrity sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=

event-target-shim@^5.0.0:
  version "5.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/event-target-shim/-/event-target-shim-5.0.1.tgz"
  integrity sha1-XU0+vflYPWOlMzzi3rdICrKwV4k=

events@^3.2.0, events@^3.3.0:
  version "3.3.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/events/-/events-3.3.0.tgz"
  integrity sha1-Mala0Kkk4tLEGagTrrLE6HjqdAA=

execa@^5.0.0:
  version "5.1.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/execa/-/execa-5.1.1.tgz"
  integrity sha1-+ArZy/Qpj3vR1MlVXCHpN0HEEd0=
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.0"
    human-signals "^2.1.0"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.1"
    onetime "^5.1.2"
    signal-exit "^3.0.3"
    strip-final-newline "^2.0.0"

exit@^0.1.2:
  version "0.1.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/exit/-/exit-0.1.2.tgz"
  integrity sha1-BjJjj42HfMghB9MKD/8aF8uhzQw=

expect@^29.0.0, expect@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/expect/-/expect-29.7.0.tgz"
  integrity sha1-V4h0WQ3LMhRRQITAgRXYruYeEbw=
  dependencies:
    "@jest/expect-utils" "^29.7.0"
    jest-get-type "^29.6.3"
    jest-matcher-utils "^29.7.0"
    jest-message-util "^29.7.0"
    jest-util "^29.7.0"

express-session@^1.18.2:
  version "1.18.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/express-session/-/express-session-1.18.2.tgz"
  integrity sha1-NNtiUmEbVwVeh3A27qCbRFPexdg=
  dependencies:
    cookie "0.7.2"
    cookie-signature "1.0.7"
    debug "2.6.9"
    depd "~2.0.0"
    on-headers "~1.1.0"
    parseurl "~1.3.3"
    safe-buffer "5.2.1"
    uid-safe "~2.1.5"

express@5.1.0:
  version "5.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/express/-/express-5.1.0.tgz"
  integrity sha1-0xvq9xWgAW8NU/R9O016zyjHXMk=
  dependencies:
    accepts "^2.0.0"
    body-parser "^2.2.0"
    content-disposition "^1.0.0"
    content-type "^1.0.5"
    cookie "^0.7.1"
    cookie-signature "^1.2.1"
    debug "^4.4.0"
    encodeurl "^2.0.0"
    escape-html "^1.0.3"
    etag "^1.8.1"
    finalhandler "^2.1.0"
    fresh "^2.0.0"
    http-errors "^2.0.0"
    merge-descriptors "^2.0.0"
    mime-types "^3.0.0"
    on-finished "^2.4.1"
    once "^1.4.0"
    parseurl "^1.3.3"
    proxy-addr "^2.0.7"
    qs "^6.14.0"
    range-parser "^1.2.1"
    router "^2.2.0"
    send "^1.1.0"
    serve-static "^2.2.0"
    statuses "^2.0.1"
    type-is "^2.0.1"
    vary "^1.1.2"

external-editor@^3.0.3, external-editor@^3.1.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/external-editor/-/external-editor-3.1.0.tgz"
  integrity sha1-ywP3QL764D6k0oPK7SdBqD8zVJU=
  dependencies:
    chardet "^0.7.0"
    iconv-lite "^0.4.24"
    tmp "^0.0.33"

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
  integrity sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=

fast-diff@^1.1.2:
  version "1.3.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/fast-diff/-/fast-diff-1.3.0.tgz"
  integrity sha1-7OQH+lUKZNY4U2zXJ+EpxhYW4PA=

fast-fifo@^1.3.2:
  version "1.3.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/fast-fifo/-/fast-fifo-1.3.2.tgz"
  integrity sha1-KG4x3pbrltOKl4mYFXQLoqTzZAw=

fast-glob@^3.2.9:
  version "3.3.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/fast-glob/-/fast-glob-3.3.3.tgz"
  integrity sha1-0G1YXOjbqQoWsFBcVDw8z7OuuBg=
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.8"

fast-json-stable-stringify@^2.0.0, fast-json-stable-stringify@^2.1.0, fast-json-stable-stringify@2.x:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
  integrity sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=

fast-levenshtein@^2.0.6:
  version "2.0.6"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

fast-safe-stringify@^2.1.1, fast-safe-stringify@2.1.1:
  version "2.1.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/fast-safe-stringify/-/fast-safe-stringify-2.1.1.tgz"
  integrity sha1-xAaoO25w2eNc47MKgRQd8wrrqIQ=

fastq@^1.6.0:
  version "1.19.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/fastq/-/fastq-1.19.1.tgz"
  integrity sha1-1Q6rqAPIhGqIPBZJKCHrzSzaVfU=
  dependencies:
    reusify "^1.0.4"

fb-watchman@^2.0.0:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/fb-watchman/-/fb-watchman-2.0.2.tgz"
  integrity sha1-6VJO5rXHfp5QAa8PhfOtu4YjJVw=
  dependencies:
    bser "2.1.1"

fflate@^0.8.2:
  version "0.8.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/fflate/-/fflate-0.8.2.tgz"
  integrity sha1-/IYx9TR4Eq1gKLvkojCLJ5KqHeo=

figures@^3.0.0, figures@^3.2.0:
  version "3.2.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/figures/-/figures-3.2.0.tgz"
  integrity sha1-YlwYvSk8YE3EqN2y/r8MiDQXRq8=
  dependencies:
    escape-string-regexp "^1.0.5"

file-entry-cache@^6.0.1:
  version "6.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/file-entry-cache/-/file-entry-cache-6.0.1.tgz"
  integrity sha1-IRst2WWcsDlLBz5zI6w8kz1SICc=
  dependencies:
    flat-cache "^3.0.4"

file-type@21.0.0:
  version "21.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/file-type/-/file-type-21.0.0.tgz"
  integrity sha1-tsWZAGS8S3BPjlybYBDFkGTSaLw=
  dependencies:
    "@tokenizer/inflate" "^0.2.7"
    strtok3 "^10.2.2"
    token-types "^6.0.0"
    uint8array-extras "^1.4.0"

fill-range@^7.1.1:
  version "7.1.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/fill-range/-/fill-range-7.1.1.tgz"
  integrity sha1-RCZdPKwH4+p9wkdRY4BkN1SgUpI=
  dependencies:
    to-regex-range "^5.0.1"

finalhandler@^2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/finalhandler/-/finalhandler-2.1.0.tgz"
  integrity sha1-cjBjc6qJ0FqCQu1WnthqG/98Vh8=
  dependencies:
    debug "^4.4.0"
    encodeurl "^2.0.0"
    escape-html "^1.0.3"
    on-finished "^2.4.1"
    parseurl "^1.3.3"
    statuses "^2.0.1"

find-up@^4.0.0:
  version "4.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/find-up/-/find-up-4.1.0.tgz"
  integrity sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

find-up@^4.1.0:
  version "4.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/find-up/-/find-up-4.1.0.tgz"
  integrity sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

find-up@^5.0.0:
  version "5.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/find-up/-/find-up-5.0.0.tgz"
  integrity sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw=
  dependencies:
    locate-path "^6.0.0"
    path-exists "^4.0.0"

flat-cache@^3.0.4:
  version "3.2.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/flat-cache/-/flat-cache-3.2.0.tgz"
  integrity sha1-LAwtUEDJmxYydxqdEFclwBFTY+4=
  dependencies:
    flatted "^3.2.9"
    keyv "^4.5.3"
    rimraf "^3.0.2"

flatted@^3.2.9:
  version "3.3.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/flatted/-/flatted-3.3.3.tgz"
  integrity sha1-Z8j62VRUp8er6/dLt47nSkQCM1g=

for-each@^0.3.5:
  version "0.3.5"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/for-each/-/for-each-0.3.5.tgz"
  integrity sha1-1lBogCeCaSD+6wr3R+57lCGkHUc=
  dependencies:
    is-callable "^1.2.7"

foreground-child@^3.1.0:
  version "3.3.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/foreground-child/-/foreground-child-3.3.1.tgz"
  integrity sha1-Mujp7Rtoo0l777msK2rfkqY4V28=
  dependencies:
    cross-spawn "^7.0.6"
    signal-exit "^4.0.1"

fork-ts-checker-webpack-plugin@9.0.2:
  version "9.0.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/fork-ts-checker-webpack-plugin/-/fork-ts-checker-webpack-plugin-9.0.2.tgz"
  integrity sha1-wSxZCVeDfrArApFpAtzz5nX9Kx4=
  dependencies:
    "@babel/code-frame" "^7.16.7"
    chalk "^4.1.2"
    chokidar "^3.5.3"
    cosmiconfig "^8.2.0"
    deepmerge "^4.2.2"
    fs-extra "^10.0.0"
    memfs "^3.4.1"
    minimatch "^3.0.4"
    node-abort-controller "^3.0.1"
    schema-utils "^3.1.1"
    semver "^7.3.5"
    tapable "^2.2.1"

form-data@^4.0.0, form-data@^4.0.4:
  version "4.0.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/form-data/-/form-data-4.0.4.tgz"
  integrity sha1-eEzczgZpqdaOlNEaxO6pgIjt0sQ=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    es-set-tostringtag "^2.1.0"
    hasown "^2.0.2"
    mime-types "^2.1.12"

formidable@^3.5.4:
  version "3.5.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/formidable/-/formidable-3.5.4.tgz"
  integrity sha1-rJpZO5UegpsymPIaqaIkOTLzLtk=
  dependencies:
    "@paralleldrive/cuid2" "^2.2.2"
    dezalgo "^1.0.4"
    once "^1.4.0"

forwarded@0.2.0:
  version "0.2.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/forwarded/-/forwarded-0.2.0.tgz"
  integrity sha1-ImmTZCiq1MFcfr6XeahL8LKoGBE=

fresh@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/fresh/-/fresh-2.0.0.tgz"
  integrity sha1-jdffahs6Gzpc8YbAWl3SZ2ImNaQ=

fs-extra@^10.0.0:
  version "10.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/fs-extra/-/fs-extra-10.1.0.tgz"
  integrity sha1-Aoc8+8QITd4SfqpfmQXu8jJdGr8=
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs-monkey@^1.0.4:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/fs-monkey/-/fs-monkey-1.1.0.tgz"
  integrity sha1-YyqhWiDnGCjtVrJDAzY/sUFOWZc=

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/fs.realpath/-/fs.realpath-1.0.0.tgz"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

function-bind@^1.1.2:
  version "1.1.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/function-bind/-/function-bind-1.1.2.tgz"
  integrity sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=

generic-pool@3.9.0:
  version "3.9.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/generic-pool/-/generic-pool-3.9.0.tgz"
  integrity sha1-NvSmeOlj9P24cH6rBQgjq8To9eQ=

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/gensync/-/gensync-1.0.0-beta.2.tgz"
  integrity sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=

get-caller-file@^2.0.5:
  version "2.0.5"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/get-caller-file/-/get-caller-file-2.0.5.tgz"
  integrity sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=

get-intrinsic@^1.2.4, get-intrinsic@^1.2.5, get-intrinsic@^1.2.6, get-intrinsic@^1.3.0:
  version "1.3.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/get-intrinsic/-/get-intrinsic-1.3.0.tgz"
  integrity sha1-dD8OO2lkqTpUke0b/6rgVNf5jQE=
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.1.1"
    function-bind "^1.1.2"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    math-intrinsics "^1.1.0"

get-package-type@^0.1.0:
  version "0.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/get-package-type/-/get-package-type-0.1.0.tgz"
  integrity sha1-jeLYA8/0TfO8bEVuZmizbDkm4Ro=

get-proto@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/get-proto/-/get-proto-1.0.1.tgz"
  integrity sha1-FQs/J0OGnvPoUewMSdFbHRTQDuE=
  dependencies:
    dunder-proto "^1.0.1"
    es-object-atoms "^1.0.0"

get-stream@^6.0.0:
  version "6.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/get-stream/-/get-stream-6.0.1.tgz"
  integrity sha1-omLY7vZ6ztV8KFKtYWdSakPL97c=

glob-parent@^5.1.2, glob-parent@~5.1.2:
  version "5.1.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/glob-parent/-/glob-parent-5.1.2.tgz"
  integrity sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=
  dependencies:
    is-glob "^4.0.1"

glob-parent@^6.0.2:
  version "6.0.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/glob-parent/-/glob-parent-6.0.2.tgz"
  integrity sha1-bSN9mQg5UMeSkPJMdkKj3poo+eM=
  dependencies:
    is-glob "^4.0.3"

glob-to-regexp@^0.4.1:
  version "0.4.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/glob-to-regexp/-/glob-to-regexp-0.4.1.tgz"
  integrity sha1-x1KXCHyFG5pXi9IX3VmpL1n+VG4=

glob@^10.4.5, glob@10.4.5:
  version "10.4.5"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/glob/-/glob-10.4.5.tgz"
  integrity sha1-9NnwuQ/9urCcnXf18ptCYlF7CVY=
  dependencies:
    foreground-child "^3.1.0"
    jackspeak "^3.1.2"
    minimatch "^9.0.4"
    minipass "^7.1.2"
    package-json-from-dist "^1.0.0"
    path-scurry "^1.11.1"

glob@^7.1.3:
  version "7.2.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/glob/-/glob-7.2.3.tgz"
  integrity sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

glob@^7.1.4:
  version "7.2.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/glob/-/glob-7.2.3.tgz"
  integrity sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globals@^13.19.0:
  version "13.24.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/globals/-/globals-13.24.0.tgz"
  integrity sha1-hDKhnXjODB6DOUnDats0VAC7EXE=
  dependencies:
    type-fest "^0.20.2"

globby@^11.1.0:
  version "11.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/globby/-/globby-11.1.0.tgz"
  integrity sha1-vUvpi7BC+D15b344EZkfvoKg00s=
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.2.9"
    ignore "^5.2.0"
    merge2 "^1.4.1"
    slash "^3.0.0"

gopd@^1.0.1, gopd@^1.2.0:
  version "1.2.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/gopd/-/gopd-1.2.0.tgz"
  integrity sha1-ifVrghe9vIgCvSmd9tfxCB1+UaE=

graceful-fs@^4.1.2, graceful-fs@^4.1.6, graceful-fs@^4.2.0, graceful-fs@^4.2.11, graceful-fs@^4.2.4, graceful-fs@^4.2.9:
  version "4.2.11"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/graceful-fs/-/graceful-fs-4.2.11.tgz"
  integrity sha1-QYPk6L8Iu24Fu7L30uDI9xLKQOM=

graphemer@^1.4.0:
  version "1.4.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/graphemer/-/graphemer-1.4.0.tgz"
  integrity sha1-+y8dVeDjoYSa7/yQxPoN1ToOZsY=

handlebars@^4.7.8:
  version "4.7.8"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/handlebars/-/handlebars-4.7.8.tgz"
  integrity sha1-QcQsGLG+I2VDkYjHfGr65xwM2ek=
  dependencies:
    minimist "^1.2.5"
    neo-async "^2.6.2"
    source-map "^0.6.1"
    wordwrap "^1.0.0"
  optionalDependencies:
    uglify-js "^3.1.4"

has-flag@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/has-flag/-/has-flag-4.0.0.tgz"
  integrity sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=

has-own-prop@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/has-own-prop/-/has-own-prop-2.0.0.tgz"
  integrity sha1-8PldWPZYBPXSGNsyVju4W44EF68=

has-property-descriptors@^1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz"
  integrity sha1-lj7X0HHce/XwhMW/vg0bYiJYaFQ=
  dependencies:
    es-define-property "^1.0.0"

has-symbols@^1.0.3, has-symbols@^1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/has-symbols/-/has-symbols-1.1.0.tgz"
  integrity sha1-/JxqeDoISVHQuXH+EBjegTcHozg=

has-tostringtag@^1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/has-tostringtag/-/has-tostringtag-1.0.2.tgz"
  integrity sha1-LNxC1AvvLltO6rfAGnPFTOerWrw=
  dependencies:
    has-symbols "^1.0.3"

hasown@^2.0.2:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/hasown/-/hasown-2.0.2.tgz"
  integrity sha1-AD6vkb563DcuhOxZ3DclLO24AAM=
  dependencies:
    function-bind "^1.1.2"

helmet@*, helmet@^8.0.0:
  version "8.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/helmet/-/helmet-8.1.0.tgz"
  integrity sha1-+W0j/tyJ6Uduy1GYGBAJyAS4s4w=

html-escaper@^2.0.0:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/html-escaper/-/html-escaper-2.0.2.tgz"
  integrity sha1-39YAJ9o2o238viNiYsAKWCJoFFM=

http-errors@^2.0.0, http-errors@2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/http-errors/-/http-errors-2.0.0.tgz"
  integrity sha1-t3dKFIbvc892Z6ya4IWMASxXudM=
  dependencies:
    depd "2.0.0"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    toidentifier "1.0.1"

http-proxy-agent@^7.0.0:
  version "7.0.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/http-proxy-agent/-/http-proxy-agent-7.0.2.tgz"
  integrity sha1-mosfJGhmwChQlIZYX2K48sGMJw4=
  dependencies:
    agent-base "^7.1.0"
    debug "^4.3.4"

https-proxy-agent@^7.0.0:
  version "7.0.6"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/https-proxy-agent/-/https-proxy-agent-7.0.6.tgz"
  integrity sha1-2o3+rH2hMLBcK6S1nJts1mYRprk=
  dependencies:
    agent-base "^7.1.2"
    debug "4"

human-signals@^2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/human-signals/-/human-signals-2.1.0.tgz"
  integrity sha1-3JH8ukLk0G5Kuu0zs+ejwC9RTqA=

iconv-lite@^0.4.24, iconv-lite@0.4.24:
  version "0.4.24"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/iconv-lite/-/iconv-lite-0.4.24.tgz"
  integrity sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

iconv-lite@^0.6.3, iconv-lite@0.6.3:
  version "0.6.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/iconv-lite/-/iconv-lite-0.6.3.tgz"
  integrity sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE=
  dependencies:
    safer-buffer ">= 2.1.2 < 3.0.0"

ieee754@^1.1.13, ieee754@^1.2.1:
  version "1.2.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ieee754/-/ieee754-1.2.1.tgz"
  integrity sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=

ignore@^5.2.0, ignore@^5.3.1:
  version "5.3.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ignore/-/ignore-5.3.2.tgz"
  integrity sha1-PNQOcp82Q/2HywTlC/DrcivFlvU=

ignore@^7.0.5:
  version "7.0.5"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ignore/-/ignore-7.0.5.tgz"
  integrity sha1-TLX2zX1MerA2VzjHrqiIuqbX79k=

import-fresh@^3.2.1, import-fresh@^3.3.0:
  version "3.3.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/import-fresh/-/import-fresh-3.3.1.tgz"
  integrity sha1-nOy1ZQPAraHydB271lRuSxO1fM8=
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

import-in-the-middle@^1.14.2:
  version "1.14.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/import-in-the-middle/-/import-in-the-middle-1.14.2.tgz"
  integrity sha1-KDZhYlqI/3wEYr0phPd3FcO8lnw=
  dependencies:
    acorn "^8.14.0"
    acorn-import-attributes "^1.9.5"
    cjs-module-lexer "^1.2.2"
    module-details-from-path "^1.0.3"

import-local@^3.0.2:
  version "3.2.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/import-local/-/import-local-3.2.0.tgz"
  integrity sha1-w9XHRXmMAqb4uJdyarpRABhu4mA=
  dependencies:
    pkg-dir "^4.2.0"
    resolve-cwd "^3.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/imurmurhash/-/imurmurhash-0.1.4.tgz"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/inflight/-/inflight-1.0.6.tgz"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@^2.0.3, inherits@^2.0.4, inherits@2, inherits@2.0.4:
  version "2.0.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/inherits/-/inherits-2.0.4.tgz"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

inquirer@8.2.6:
  version "8.2.6"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/inquirer/-/inquirer-8.2.6.tgz"
  integrity sha1-czt0iIGV2NQApnrDMgEbX65epWI=
  dependencies:
    ansi-escapes "^4.2.1"
    chalk "^4.1.1"
    cli-cursor "^3.1.0"
    cli-width "^3.0.0"
    external-editor "^3.0.3"
    figures "^3.0.0"
    lodash "^4.17.21"
    mute-stream "0.0.8"
    ora "^5.4.1"
    run-async "^2.4.0"
    rxjs "^7.5.5"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"
    through "^2.3.6"
    wrap-ansi "^6.0.1"

inquirer@9.2.15:
  version "9.2.15"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/inquirer/-/inquirer-9.2.15.tgz"
  integrity sha1-ITWjYZCm5ckvXSBeCvH+o2udNJI=
  dependencies:
    "@ljharb/through" "^2.3.12"
    ansi-escapes "^4.3.2"
    chalk "^5.3.0"
    cli-cursor "^3.1.0"
    cli-width "^4.1.0"
    external-editor "^3.1.0"
    figures "^3.2.0"
    lodash "^4.17.21"
    mute-stream "1.0.0"
    ora "^5.4.1"
    run-async "^3.0.0"
    rxjs "^7.8.1"
    string-width "^4.2.3"
    strip-ansi "^6.0.1"
    wrap-ansi "^6.2.0"

ipaddr.js@1.9.1:
  version "1.9.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ipaddr.js/-/ipaddr.js-1.9.1.tgz"
  integrity sha1-v/OFQ+64mEglB5/zoqjmy9RngbM=

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/is-arrayish/-/is-arrayish-0.2.1.tgz"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/is-binary-path/-/is-binary-path-2.1.0.tgz"
  integrity sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=
  dependencies:
    binary-extensions "^2.0.0"

is-callable@^1.2.7:
  version "1.2.7"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/is-callable/-/is-callable-1.2.7.tgz"
  integrity sha1-O8KoXqdC2eNiBdys3XLKH9xRsFU=

is-core-module@^2.16.0:
  version "2.16.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/is-core-module/-/is-core-module-2.16.1.tgz"
  integrity sha1-KpiAGoSfQ+Kt1kT7trxiKbGaTvQ=
  dependencies:
    hasown "^2.0.2"

is-docker@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/is-docker/-/is-docker-3.0.0.tgz"
  integrity sha1-kAk6oxBid9inelkQ265xdH4VogA=

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/is-extglob/-/is-extglob-2.1.1.tgz"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"
  integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=

is-generator-fn@^2.0.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/is-generator-fn/-/is-generator-fn-2.1.0.tgz"
  integrity sha1-fRQK3DiarzARqPKipM+m+q3/sRg=

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
  version "4.0.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/is-glob/-/is-glob-4.0.3.tgz"
  integrity sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=
  dependencies:
    is-extglob "^2.1.1"

is-inside-container@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/is-inside-container/-/is-inside-container-1.0.0.tgz"
  integrity sha1-6B+6aZZi6zHb2vJnZqYdSBRxfqQ=
  dependencies:
    is-docker "^3.0.0"

is-interactive@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/is-interactive/-/is-interactive-1.0.0.tgz"
  integrity sha1-zqbmrlyHCnsKAAQHC3tYfgJSkS4=

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/is-number/-/is-number-7.0.0.tgz"
  integrity sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=

is-path-inside@^3.0.3:
  version "3.0.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/is-path-inside/-/is-path-inside-3.0.3.tgz"
  integrity sha1-0jE2LlOgf/Kw4Op/7QSRYf/RYoM=

is-promise@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/is-promise/-/is-promise-4.0.0.tgz"
  integrity sha1-Qv+fhCBsGZHSbev1IN1cAQQt0vM=

is-stream@^2.0.0:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/is-stream/-/is-stream-2.0.1.tgz"
  integrity sha1-+sHj1TuXrVqdCunO8jifWBClwHc=

is-typed-array@^1.1.14:
  version "1.1.15"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/is-typed-array/-/is-typed-array-1.1.15.tgz"
  integrity sha1-S/tKRbYc7oOlpG+6d45OjVnAzgs=
  dependencies:
    which-typed-array "^1.1.16"

is-unicode-supported@^0.1.0:
  version "0.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/is-unicode-supported/-/is-unicode-supported-0.1.0.tgz"
  integrity sha1-PybHaoCVk7Ur+i7LVxDtJ3m1Iqc=

is-wsl@^3.1.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/is-wsl/-/is-wsl-3.1.0.tgz"
  integrity sha1-4cZX45wQCQr8vt7GFyD2uSTDy9I=
  dependencies:
    is-inside-container "^1.0.0"

isarray@^2.0.5:
  version "2.0.5"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/isarray/-/isarray-2.0.5.tgz"
  integrity sha1-ivHkwSISRMxiRZ+vOJQNTmRKVyM=

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/isexe/-/isexe-2.0.0.tgz"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

istanbul-lib-coverage@^3.0.0, istanbul-lib-coverage@^3.2.0, istanbul-lib-coverage@^3.2.2:
  version "3.2.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.2.tgz"
  integrity sha1-LRZsSwZE1Do58Ev2wu3R5YXzF1Y=

istanbul-lib-instrument@^5.0.4:
  version "5.2.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/istanbul-lib-instrument/-/istanbul-lib-instrument-5.2.1.tgz"
  integrity sha1-0QyIhcISVXThwjHKyt+VVnXhzj0=
  dependencies:
    "@babel/core" "^7.12.3"
    "@babel/parser" "^7.14.7"
    "@istanbuljs/schema" "^0.1.2"
    istanbul-lib-coverage "^3.2.0"
    semver "^6.3.0"

istanbul-lib-instrument@^6.0.0:
  version "6.0.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/istanbul-lib-instrument/-/istanbul-lib-instrument-6.0.3.tgz"
  integrity sha1-+hVAHfbBWHS8shBfdzMl14xmZ2U=
  dependencies:
    "@babel/core" "^7.23.9"
    "@babel/parser" "^7.23.9"
    "@istanbuljs/schema" "^0.1.3"
    istanbul-lib-coverage "^3.2.0"
    semver "^7.5.4"

istanbul-lib-report@^3.0.0:
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/istanbul-lib-report/-/istanbul-lib-report-3.0.1.tgz"
  integrity sha1-kIMFusmlvRdaxqdEier9D8JEWn0=
  dependencies:
    istanbul-lib-coverage "^3.0.0"
    make-dir "^4.0.0"
    supports-color "^7.1.0"

istanbul-lib-source-maps@^4.0.0:
  version "4.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/istanbul-lib-source-maps/-/istanbul-lib-source-maps-4.0.1.tgz"
  integrity sha1-iV86cJ/PujTG3lpCk5Ai8+Q1hVE=
  dependencies:
    debug "^4.1.1"
    istanbul-lib-coverage "^3.0.0"
    source-map "^0.6.1"

istanbul-reports@^3.1.3:
  version "3.1.7"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/istanbul-reports/-/istanbul-reports-3.1.7.tgz"
  integrity sha1-2u0SueHcpRjhXAVuHlN+dBKA+gs=
  dependencies:
    html-escaper "^2.0.0"
    istanbul-lib-report "^3.0.0"

iterare@1.2.1:
  version "1.2.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/iterare/-/iterare-1.2.1.tgz"
  integrity sha1-E5xAD/c2NpDjOr/6M8u6iSDwAEI=

jackspeak@^3.1.2:
  version "3.4.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jackspeak/-/jackspeak-3.4.3.tgz"
  integrity sha1-iDOp2Jq0rN5hiJQr0cU7Y5DtWoo=
  dependencies:
    "@isaacs/cliui" "^8.0.2"
  optionalDependencies:
    "@pkgjs/parseargs" "^0.11.0"

jest-changed-files@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-changed-files/-/jest-changed-files-29.7.0.tgz"
  integrity sha1-HAbQfnfHjhWF0CBCTe3BDW4XrDo=
  dependencies:
    execa "^5.0.0"
    jest-util "^29.7.0"
    p-limit "^3.1.0"

jest-circus@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-circus/-/jest-circus-29.7.0.tgz"
  integrity sha1-toF6RfzINdixbVli0MAmRz7jZoo=
  dependencies:
    "@jest/environment" "^29.7.0"
    "@jest/expect" "^29.7.0"
    "@jest/test-result" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    chalk "^4.0.0"
    co "^4.6.0"
    dedent "^1.0.0"
    is-generator-fn "^2.0.0"
    jest-each "^29.7.0"
    jest-matcher-utils "^29.7.0"
    jest-message-util "^29.7.0"
    jest-runtime "^29.7.0"
    jest-snapshot "^29.7.0"
    jest-util "^29.7.0"
    p-limit "^3.1.0"
    pretty-format "^29.7.0"
    pure-rand "^6.0.0"
    slash "^3.0.0"
    stack-utils "^2.0.3"

jest-cli@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-cli/-/jest-cli-29.7.0.tgz"
  integrity sha1-VZLJQHmODK5nfuwWkmTy2DmjeZU=
  dependencies:
    "@jest/core" "^29.7.0"
    "@jest/test-result" "^29.7.0"
    "@jest/types" "^29.6.3"
    chalk "^4.0.0"
    create-jest "^29.7.0"
    exit "^0.1.2"
    import-local "^3.0.2"
    jest-config "^29.7.0"
    jest-util "^29.7.0"
    jest-validate "^29.7.0"
    yargs "^17.3.1"

jest-config@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-config/-/jest-config-29.7.0.tgz"
  integrity sha1-vL2ogG28wBseMWpGu3QIWoSwJF8=
  dependencies:
    "@babel/core" "^7.11.6"
    "@jest/test-sequencer" "^29.7.0"
    "@jest/types" "^29.6.3"
    babel-jest "^29.7.0"
    chalk "^4.0.0"
    ci-info "^3.2.0"
    deepmerge "^4.2.2"
    glob "^7.1.3"
    graceful-fs "^4.2.9"
    jest-circus "^29.7.0"
    jest-environment-node "^29.7.0"
    jest-get-type "^29.6.3"
    jest-regex-util "^29.6.3"
    jest-resolve "^29.7.0"
    jest-runner "^29.7.0"
    jest-util "^29.7.0"
    jest-validate "^29.7.0"
    micromatch "^4.0.4"
    parse-json "^5.2.0"
    pretty-format "^29.7.0"
    slash "^3.0.0"
    strip-json-comments "^3.1.1"

jest-diff@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-diff/-/jest-diff-29.7.0.tgz"
  integrity sha1-AXk0pm67fs9vIF6EaZvhCv1wRYo=
  dependencies:
    chalk "^4.0.0"
    diff-sequences "^29.6.3"
    jest-get-type "^29.6.3"
    pretty-format "^29.7.0"

jest-docblock@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-docblock/-/jest-docblock-29.7.0.tgz"
  integrity sha1-j922rcPNyVXJPiqH9hz9NQ1dEZo=
  dependencies:
    detect-newline "^3.0.0"

jest-each@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-each/-/jest-each-29.7.0.tgz"
  integrity sha1-FiqbPyMovdmRvqq/+7dHReVld9E=
  dependencies:
    "@jest/types" "^29.6.3"
    chalk "^4.0.0"
    jest-get-type "^29.6.3"
    jest-util "^29.7.0"
    pretty-format "^29.7.0"

jest-environment-node@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-environment-node/-/jest-environment-node-29.7.0.tgz"
  integrity sha1-C5PhEd2o7BILyDAObR+5V24WQ3Y=
  dependencies:
    "@jest/environment" "^29.7.0"
    "@jest/fake-timers" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    jest-mock "^29.7.0"
    jest-util "^29.7.0"

jest-get-type@^29.6.3:
  version "29.6.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-get-type/-/jest-get-type-29.6.3.tgz"
  integrity sha1-NvSZ/c6hl8EEWhJzGcBIFyOQj9E=

jest-haste-map@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-haste-map/-/jest-haste-map-29.7.0.tgz"
  integrity sha1-PCOWUkSC9aBQY3bmyFjDu8wXsQQ=
  dependencies:
    "@jest/types" "^29.6.3"
    "@types/graceful-fs" "^4.1.3"
    "@types/node" "*"
    anymatch "^3.0.3"
    fb-watchman "^2.0.0"
    graceful-fs "^4.2.9"
    jest-regex-util "^29.6.3"
    jest-util "^29.7.0"
    jest-worker "^29.7.0"
    micromatch "^4.0.4"
    walker "^1.0.8"
  optionalDependencies:
    fsevents "^2.3.2"

jest-leak-detector@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-leak-detector/-/jest-leak-detector-29.7.0.tgz"
  integrity sha1-W37A2t/f7Ayjg9yaoBbTa16kxyg=
  dependencies:
    jest-get-type "^29.6.3"
    pretty-format "^29.7.0"

jest-matcher-utils@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-matcher-utils/-/jest-matcher-utils-29.7.0.tgz"
  integrity sha1-ro/sef8kn9WSzoDj7kdOg6bETxI=
  dependencies:
    chalk "^4.0.0"
    jest-diff "^29.7.0"
    jest-get-type "^29.6.3"
    pretty-format "^29.7.0"

jest-message-util@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-message-util/-/jest-message-util-29.7.0.tgz"
  integrity sha1-i8OS4gTpXf51ZKu+cqQE4o5R9/M=
  dependencies:
    "@babel/code-frame" "^7.12.13"
    "@jest/types" "^29.6.3"
    "@types/stack-utils" "^2.0.0"
    chalk "^4.0.0"
    graceful-fs "^4.2.9"
    micromatch "^4.0.4"
    pretty-format "^29.7.0"
    slash "^3.0.0"
    stack-utils "^2.0.3"

jest-mock@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-mock/-/jest-mock-29.7.0.tgz"
  integrity sha1-ToNs9g6Zxvz6vp+Z0Bfz/dUKY0c=
  dependencies:
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    jest-util "^29.7.0"

jest-pnp-resolver@^1.2.2:
  version "1.2.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-pnp-resolver/-/jest-pnp-resolver-1.2.3.tgz"
  integrity sha1-kwsVRhZNStWTfVVA5xHU041MrS4=

jest-regex-util@^29.6.3:
  version "29.6.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-regex-util/-/jest-regex-util-29.6.3.tgz"
  integrity sha1-SlVtnHdq9o4cX0gZT00DJ9JOilI=

jest-resolve-dependencies@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-resolve-dependencies/-/jest-resolve-dependencies-29.7.0.tgz"
  integrity sha1-GwTywJXzf8d2/0CAPckpIbHohCg=
  dependencies:
    jest-regex-util "^29.6.3"
    jest-snapshot "^29.7.0"

jest-resolve@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-resolve/-/jest-resolve-29.7.0.tgz"
  integrity sha1-ZNaomS3Sb2NasMAeXu9Dmca8vDA=
  dependencies:
    chalk "^4.0.0"
    graceful-fs "^4.2.9"
    jest-haste-map "^29.7.0"
    jest-pnp-resolver "^1.2.2"
    jest-util "^29.7.0"
    jest-validate "^29.7.0"
    resolve "^1.20.0"
    resolve.exports "^2.0.0"
    slash "^3.0.0"

jest-runner@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-runner/-/jest-runner-29.7.0.tgz"
  integrity sha1-gJrwctQIpT3P0uhJpMl20xMvcY4=
  dependencies:
    "@jest/console" "^29.7.0"
    "@jest/environment" "^29.7.0"
    "@jest/test-result" "^29.7.0"
    "@jest/transform" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    chalk "^4.0.0"
    emittery "^0.13.1"
    graceful-fs "^4.2.9"
    jest-docblock "^29.7.0"
    jest-environment-node "^29.7.0"
    jest-haste-map "^29.7.0"
    jest-leak-detector "^29.7.0"
    jest-message-util "^29.7.0"
    jest-resolve "^29.7.0"
    jest-runtime "^29.7.0"
    jest-util "^29.7.0"
    jest-watcher "^29.7.0"
    jest-worker "^29.7.0"
    p-limit "^3.1.0"
    source-map-support "0.5.13"

jest-runtime@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-runtime/-/jest-runtime-29.7.0.tgz"
  integrity sha1-7+yzFBz303Z6OgzI98mZBYfT2Bc=
  dependencies:
    "@jest/environment" "^29.7.0"
    "@jest/fake-timers" "^29.7.0"
    "@jest/globals" "^29.7.0"
    "@jest/source-map" "^29.6.3"
    "@jest/test-result" "^29.7.0"
    "@jest/transform" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    chalk "^4.0.0"
    cjs-module-lexer "^1.0.0"
    collect-v8-coverage "^1.0.0"
    glob "^7.1.3"
    graceful-fs "^4.2.9"
    jest-haste-map "^29.7.0"
    jest-message-util "^29.7.0"
    jest-mock "^29.7.0"
    jest-regex-util "^29.6.3"
    jest-resolve "^29.7.0"
    jest-snapshot "^29.7.0"
    jest-util "^29.7.0"
    slash "^3.0.0"
    strip-bom "^4.0.0"

jest-snapshot@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-snapshot/-/jest-snapshot-29.7.0.tgz"
  integrity sha1-wsV0w/UYZdobsykDZ3imm/iKa+U=
  dependencies:
    "@babel/core" "^7.11.6"
    "@babel/generator" "^7.7.2"
    "@babel/plugin-syntax-jsx" "^7.7.2"
    "@babel/plugin-syntax-typescript" "^7.7.2"
    "@babel/types" "^7.3.3"
    "@jest/expect-utils" "^29.7.0"
    "@jest/transform" "^29.7.0"
    "@jest/types" "^29.6.3"
    babel-preset-current-node-syntax "^1.0.0"
    chalk "^4.0.0"
    expect "^29.7.0"
    graceful-fs "^4.2.9"
    jest-diff "^29.7.0"
    jest-get-type "^29.6.3"
    jest-matcher-utils "^29.7.0"
    jest-message-util "^29.7.0"
    jest-util "^29.7.0"
    natural-compare "^1.4.0"
    pretty-format "^29.7.0"
    semver "^7.5.3"

jest-util@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-util/-/jest-util-29.7.0.tgz"
  integrity sha1-I8K2K/sivoK0TemAVYAv83EPwLw=
  dependencies:
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    chalk "^4.0.0"
    ci-info "^3.2.0"
    graceful-fs "^4.2.9"
    picomatch "^2.2.3"

jest-validate@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-validate/-/jest-validate-29.7.0.tgz"
  integrity sha1-e/cFURxk2lkdRrFfzkFADVIUfZw=
  dependencies:
    "@jest/types" "^29.6.3"
    camelcase "^6.2.0"
    chalk "^4.0.0"
    jest-get-type "^29.6.3"
    leven "^3.1.0"
    pretty-format "^29.7.0"

jest-watcher@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-watcher/-/jest-watcher-29.7.0.tgz"
  integrity sha1-eBDTDWGcOmIJMiPOa7NZyhsoovI=
  dependencies:
    "@jest/test-result" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    ansi-escapes "^4.2.1"
    chalk "^4.0.0"
    emittery "^0.13.1"
    jest-util "^29.7.0"
    string-length "^4.0.1"

jest-worker@^27.4.5:
  version "27.5.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-worker/-/jest-worker-27.5.1.tgz"
  integrity sha1-jRRvCQDolzsQa29zzB6ajLhvjbA=
  dependencies:
    "@types/node" "*"
    merge-stream "^2.0.0"
    supports-color "^8.0.0"

jest-worker@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-worker/-/jest-worker-29.7.0.tgz"
  integrity sha1-rK0HOsu663JivVOJ4bz0PhAFjUo=
  dependencies:
    "@types/node" "*"
    jest-util "^29.7.0"
    merge-stream "^2.0.0"
    supports-color "^8.0.0"

jest@^29.5.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest/-/jest-29.7.0.tgz"
  integrity sha1-mUZ2/CQXfwiPHF43N/VpcgT/JhM=
  dependencies:
    "@jest/core" "^29.7.0"
    "@jest/types" "^29.6.3"
    import-local "^3.0.2"
    jest-cli "^29.7.0"

js-md4@^0.3.2:
  version "0.3.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/js-md4/-/js-md4-0.3.2.tgz"
  integrity sha1-zTs9wEWwxARVbIHdtXVsI+WdfPU=

js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/js-tokens/-/js-tokens-4.0.0.tgz"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

js-yaml@^3.13.1:
  version "3.14.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/js-yaml/-/js-yaml-3.14.1.tgz"
  integrity sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

js-yaml@^4.1.0, js-yaml@4.1.0:
  version "4.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/js-yaml/-/js-yaml-4.1.0.tgz"
  integrity sha1-wftl+PUBeQHN0slRhkuhhFihBgI=
  dependencies:
    argparse "^2.0.1"

jsep@^1.4.0:
  version "1.4.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jsep/-/jsep-1.4.0.tgz"
  integrity sha1-Gf7Mv6Udinn3JIC0uOQM4uFxUvA=

jsesc@^3.0.2:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jsesc/-/jsesc-3.1.0.tgz"
  integrity sha1-dNM1ojT2ftGZB/2t+sfM+dQJgl0=

json-buffer@3.0.1:
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/json-buffer/-/json-buffer-3.0.1.tgz"
  integrity sha1-kziAKjDTtmBfvgYT4JQAjKjAWhM=

json-parse-even-better-errors@^2.3.0, json-parse-even-better-errors@^2.3.1:
  version "2.3.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz"
  integrity sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-schema-traverse@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz"
  integrity sha1-rnvLNlard6c7pcSb9lTzjmtoYOI=

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

json5@^2.2.2, json5@^2.2.3:
  version "2.2.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/json5/-/json5-2.2.3.tgz"
  integrity sha1-eM1vGhm9wStz21rQxh79ZsHikoM=

jsonc-parser@3.2.1:
  version "3.2.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jsonc-parser/-/jsonc-parser-3.2.1.tgz"
  integrity sha1-AxkEVxzPkp12cO6MVHVFCByzfxo=

jsonc-parser@3.3.1:
  version "3.3.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jsonc-parser/-/jsonc-parser-3.3.1.tgz"
  integrity sha1-8qUktPf9EePXkeVZl3rWC5i3mLQ=

jsonfile@^6.0.1:
  version "6.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jsonfile/-/jsonfile-6.1.0.tgz"
  integrity sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonpath-plus@^10.3.0:
  version "10.3.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jsonpath-plus/-/jsonpath-plus-10.3.0.tgz"
  integrity sha1-WeIuT6IpjGjfzXBlm7R/DK1SUjg=
  dependencies:
    "@jsep-plugin/assignment" "^1.3.0"
    "@jsep-plugin/regex" "^1.0.4"
    jsep "^1.4.0"

jsonwebtoken@^9.0.0, jsonwebtoken@9.0.2:
  version "9.0.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jsonwebtoken/-/jsonwebtoken-9.0.2.tgz"
  integrity sha1-Zf+R9KvvF4RpfUCVK7GZjFBMqvM=
  dependencies:
    jws "^3.2.2"
    lodash.includes "^4.3.0"
    lodash.isboolean "^3.0.3"
    lodash.isinteger "^4.0.4"
    lodash.isnumber "^3.0.3"
    lodash.isplainobject "^4.0.6"
    lodash.isstring "^4.0.1"
    lodash.once "^4.0.0"
    ms "^2.1.1"
    semver "^7.5.4"

jwa@^1.4.1:
  version "1.4.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jwa/-/jwa-1.4.2.tgz"
  integrity sha1-FgEaxttI3nsQJ3fleJeQFSDux7k=
  dependencies:
    buffer-equal-constant-time "^1.0.1"
    ecdsa-sig-formatter "1.0.11"
    safe-buffer "^5.0.1"

jws@^3.2.2:
  version "3.2.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jws/-/jws-3.2.2.tgz"
  integrity sha1-ABCZ82OUaMlBQADpmZX6UvtHgwQ=
  dependencies:
    jwa "^1.4.1"
    safe-buffer "^5.0.1"

keyv@^4.5.3:
  version "4.5.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/keyv/-/keyv-4.5.4.tgz"
  integrity sha1-qHmpnilFL5QkOfKkBeOvizHU3pM=
  dependencies:
    json-buffer "3.0.1"

kleur@^3.0.3:
  version "3.0.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/kleur/-/kleur-3.0.3.tgz"
  integrity sha1-p5yezIbuHOP6YgbRIWxQHxR/wH4=

koalas@^1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/koalas/-/koalas-1.0.2.tgz"
  integrity sha1-MYQz8HQjXbePrlZhoCqMpT7ilc0=

leven@^3.1.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/leven/-/leven-3.1.0.tgz"
  integrity sha1-d4kd6DQGTMy6gq54QrtrFKE+1/I=

levn@^0.4.1:
  version "0.4.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/levn/-/levn-0.4.1.tgz"
  integrity sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

libphonenumber-js@^1.11.1:
  version "1.12.10"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/libphonenumber-js/-/libphonenumber-js-1.12.10.tgz"
  integrity sha1-Ul98n/rqQMIUY9OQb1LsMgnqWUU=

limiter@^1.1.5:
  version "1.1.5"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/limiter/-/limiter-1.1.5.tgz"
  integrity sha1-j5KiWzsWxhMSk6DMg0tKg4oqp8I=

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/lines-and-columns/-/lines-and-columns-1.2.4.tgz"
  integrity sha1-7KKE910pZQeTCdwK2SVauy68FjI=

load-esm@1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/load-esm/-/load-esm-1.0.2.tgz"
  integrity sha1-Ndusiho6vbgCzyNgCASPzIqSiaY=

loader-runner@^4.2.0:
  version "4.3.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/loader-runner/-/loader-runner-4.3.0.tgz"
  integrity sha1-wbShY7mfYUgwNTsWdV5xSawjFOE=

locate-path@^5.0.0:
  version "5.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/locate-path/-/locate-path-5.0.0.tgz"
  integrity sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=
  dependencies:
    p-locate "^4.1.0"

locate-path@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/locate-path/-/locate-path-6.0.0.tgz"
  integrity sha1-VTIeswn+u8WcSAHZMackUqaB0oY=
  dependencies:
    p-locate "^5.0.0"

lodash.includes@^4.3.0:
  version "4.3.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/lodash.includes/-/lodash.includes-4.3.0.tgz"
  integrity sha1-YLuYqHy5I8aMoeUTJUgzFISfVT8=

lodash.isboolean@^3.0.3:
  version "3.0.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/lodash.isboolean/-/lodash.isboolean-3.0.3.tgz"
  integrity sha1-bC4XHbKiV82WgC/UOwGyDV9YcPY=

lodash.isinteger@^4.0.4:
  version "4.0.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/lodash.isinteger/-/lodash.isinteger-4.0.4.tgz"
  integrity sha1-YZwK89A/iwTDH1iChAt3sRzWg0M=

lodash.isnumber@^3.0.3:
  version "3.0.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/lodash.isnumber/-/lodash.isnumber-3.0.3.tgz"
  integrity sha1-POdoEMWSjQM1IwGsKHMX8RwLH/w=

lodash.isplainobject@^4.0.6:
  version "4.0.6"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz"
  integrity sha1-fFJqUtibRcRcxpC4gWO+BJf1UMs=

lodash.isstring@^4.0.1:
  version "4.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/lodash.isstring/-/lodash.isstring-4.0.1.tgz"
  integrity sha1-1SfftUVuynzJu5XV2ur4i6VKVFE=

lodash.memoize@^4.1.2:
  version "4.1.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/lodash.memoize/-/lodash.memoize-4.1.2.tgz"
  integrity sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4=

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/lodash.merge/-/lodash.merge-4.6.2.tgz"
  integrity sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=

lodash.once@^4.0.0:
  version "4.1.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/lodash.once/-/lodash.once-4.1.1.tgz"
  integrity sha1-DdOXEhPHxW34gJd9UEyI+0cal6w=

lodash.sortby@^4.7.0:
  version "4.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/lodash.sortby/-/lodash.sortby-4.7.0.tgz"
  integrity sha1-7dFMgk4sycHgsKG0K7UhBRakJDg=

lodash@^4.17.21, lodash@4.17.21:
  version "4.17.21"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/lodash/-/lodash-4.17.21.tgz"
  integrity sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=

log-symbols@^4.1.0:
  version "4.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/log-symbols/-/log-symbols-4.1.0.tgz"
  integrity sha1-P727lbRoOsn8eFER55LlWNSr1QM=
  dependencies:
    chalk "^4.1.0"
    is-unicode-supported "^0.1.0"

long@^5.0.0:
  version "5.3.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/long/-/long-5.3.2.tgz"
  integrity sha1-HYRGMJWZkmLX17f4v9SozFUWf4M=

lru-cache@^10.2.0, lru-cache@^10.4.3:
  version "10.4.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/lru-cache/-/lru-cache-10.4.3.tgz"
  integrity sha1-QQ/IoXtw5ZgBPfJXwkRrfzOD8Rk=

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/lru-cache/-/lru-cache-5.1.1.tgz"
  integrity sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=
  dependencies:
    yallist "^3.0.2"

lru-cache@^7.14.0:
  version "7.18.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/lru-cache/-/lru-cache-7.18.3.tgz"
  integrity sha1-95OJbg/Q6VSlnf3YLwdzgI32qok=

magic-string@0.30.8:
  version "0.30.8"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/magic-string/-/magic-string-0.30.8.tgz"
  integrity sha1-FOhiQkbSvtunDVRiqpmsloGERhM=
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.4.15"

make-dir@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/make-dir/-/make-dir-4.0.0.tgz"
  integrity sha1-w8IwencSd82WODBfkVwprnQbYU4=
  dependencies:
    semver "^7.5.3"

make-error@^1.1.1, make-error@^1.3.6:
  version "1.3.6"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/make-error/-/make-error-1.3.6.tgz"
  integrity sha1-LrLjfqm2fEiR9oShOUeZr0hM96I=

makeerror@1.0.12:
  version "1.0.12"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/makeerror/-/makeerror-1.0.12.tgz"
  integrity sha1-Pl3SB5qC6BLpg8xmEMSiyw6qgBo=
  dependencies:
    tmpl "1.0.5"

math-intrinsics@^1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/math-intrinsics/-/math-intrinsics-1.1.0.tgz"
  integrity sha1-oN10voHiqlwvJ+Zc4oNgXuTit/k=

media-typer@^1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/media-typer/-/media-typer-1.1.0.tgz"
  integrity sha1-ardLjy0zIPIGSyqHo455Mf86VWE=

media-typer@0.3.0:
  version "0.3.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/media-typer/-/media-typer-0.3.0.tgz"
  integrity sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=

memfs@^3.4.1:
  version "3.6.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/memfs/-/memfs-3.6.0.tgz"
  integrity sha1-16IRD4b3ndlQqLbfbVe8mEqhhfY=
  dependencies:
    fs-monkey "^1.0.4"

merge-descriptors@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/merge-descriptors/-/merge-descriptors-2.0.0.tgz"
  integrity sha1-6pIvZgY1oiSe5WXgRJ+VHmtgOAg=

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/merge-stream/-/merge-stream-2.0.0.tgz"
  integrity sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=

merge2@^1.3.0, merge2@^1.4.1:
  version "1.4.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/merge2/-/merge2-1.4.1.tgz"
  integrity sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=

methods@^1.1.2:
  version "1.1.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/methods/-/methods-1.1.2.tgz"
  integrity sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=

micromatch@^4.0.0, micromatch@^4.0.4, micromatch@^4.0.8:
  version "4.0.8"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/micromatch/-/micromatch-4.0.8.tgz"
  integrity sha1-1m+hjzpHB2eJMgubGvMr2G2fogI=
  dependencies:
    braces "^3.0.3"
    picomatch "^2.3.1"

mime-db@^1.54.0, "mime-db@>= 1.43.0 < 2":
  version "1.54.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/mime-db/-/mime-db-1.54.0.tgz"
  integrity sha1-zds+5PnGRTDf9kAjZmHULLajFPU=

mime-db@1.52.0:
  version "1.52.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/mime-db/-/mime-db-1.52.0.tgz"
  integrity sha1-u6vNwChZ9JhzAchW4zh85exDv3A=

mime-types@^2.1.12:
  version "2.1.35"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/mime-types/-/mime-types-2.1.35.tgz"
  integrity sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=
  dependencies:
    mime-db "1.52.0"

mime-types@^2.1.27:
  version "2.1.35"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/mime-types/-/mime-types-2.1.35.tgz"
  integrity sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=
  dependencies:
    mime-db "1.52.0"

mime-types@^3.0.0, mime-types@^3.0.1:
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/mime-types/-/mime-types-3.0.1.tgz"
  integrity sha1-sdlNaZepsy/WnrrtDbc96Ky1Gc4=
  dependencies:
    mime-db "^1.54.0"

mime-types@~2.1.24:
  version "2.1.35"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/mime-types/-/mime-types-2.1.35.tgz"
  integrity sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=
  dependencies:
    mime-db "1.52.0"

mime@2.6.0:
  version "2.6.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/mime/-/mime-2.6.0.tgz"
  integrity sha1-oqaCqVzU0MsdYlfij4PafjWAA2c=

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/mimic-fn/-/mimic-fn-2.1.0.tgz"
  integrity sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=

minimatch@^3.0.4, minimatch@^3.1.1:
  version "3.1.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/minimatch/-/minimatch-3.1.2.tgz"
  integrity sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^3.0.5:
  version "3.1.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/minimatch/-/minimatch-3.1.2.tgz"
  integrity sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^3.1.2:
  version "3.1.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/minimatch/-/minimatch-3.1.2.tgz"
  integrity sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^9.0.4:
  version "9.0.5"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/minimatch/-/minimatch-9.0.5.tgz"
  integrity sha1-10+d1rV9g9jpjPuCEzsDl4vJKeU=
  dependencies:
    brace-expansion "^2.0.1"

minimist@^1.2.5, minimist@^1.2.6:
  version "1.2.8"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/minimist/-/minimist-1.2.8.tgz"
  integrity sha1-waRk52kzAuCCoHXO4MBXdBrEdyw=

"minipass@^5.0.0 || ^6.0.2 || ^7.0.0", minipass@^7.1.2:
  version "7.1.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/minipass/-/minipass-7.1.2.tgz"
  integrity sha1-k6libOXl5mvU24aEnnUV6SNApwc=

mkdirp@^0.5.6:
  version "0.5.6"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/mkdirp/-/mkdirp-0.5.6.tgz"
  integrity sha1-fe8D0kMtyuS6HWEURcSDlgYiVfY=
  dependencies:
    minimist "^1.2.6"

module-details-from-path@^1.0.3, module-details-from-path@^1.0.4:
  version "1.0.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/module-details-from-path/-/module-details-from-path-1.0.4.tgz"
  integrity sha1-tmL9zZP2yD0/JSidoM6ByNloW5Q=

ms@^2.1.1, ms@^2.1.3:
  version "2.1.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ms/-/ms-2.1.3.tgz"
  integrity sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=

ms@2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ms/-/ms-2.0.0.tgz"
  integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=

mssql@^11.0.1:
  version "11.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/mssql/-/mssql-11.0.1.tgz"
  integrity sha1-oyq3djv7s/XZcOR1Y985EfwE4h0=
  dependencies:
    "@tediousjs/connection-string" "^0.5.0"
    commander "^11.0.0"
    debug "^4.3.3"
    rfdc "^1.3.0"
    tarn "^3.0.2"
    tedious "^18.2.1"

multer@2.0.2:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/multer/-/multer-2.0.2.tgz"
  integrity sha1-CKiqglWGU4jDh6rwQUJrDIe/WN0=
  dependencies:
    append-field "^1.0.0"
    busboy "^1.6.0"
    concat-stream "^2.0.0"
    mkdirp "^0.5.6"
    object-assign "^4.1.1"
    type-is "^1.6.18"
    xtend "^4.0.2"

mute-stream@0.0.8:
  version "0.0.8"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/mute-stream/-/mute-stream-0.0.8.tgz"
  integrity sha1-FjDEKyJR/4HiooPelqVJfqkuXg0=

mute-stream@1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/mute-stream/-/mute-stream-1.0.0.tgz"
  integrity sha1-4xvZ/mLwrtI1IKpDJOpmcVMeAT4=

mutexify@^1.4.0:
  version "1.4.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/mutexify/-/mutexify-1.4.0.tgz"
  integrity sha1-t/SsAnPIGCS4QIh8am4L+rFLvpQ=
  dependencies:
    queue-tick "^1.0.0"

native-duplexpair@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/native-duplexpair/-/native-duplexpair-1.0.0.tgz"
  integrity sha1-eJkHjmS/PIo9cyYBs9QP8F21j6A=

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/natural-compare/-/natural-compare-1.4.0.tgz"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

negotiator@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/negotiator/-/negotiator-1.0.0.tgz"
  integrity sha1-tskbtHFy1p+Tz9fDV7u1KQGbX2o=

negotiator@~0.6.4:
  version "0.6.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/negotiator/-/negotiator-0.6.4.tgz"
  integrity sha1-d3lI4kUmUcVwtxLdAcI+JicT//c=

neo-async@^2.6.2:
  version "2.6.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/neo-async/-/neo-async-2.6.2.tgz"
  integrity sha1-tKr7k+OustgXTKU88WOrfXMIMF8=

node-abort-controller@^3.0.1:
  version "3.1.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/node-abort-controller/-/node-abort-controller-3.1.1.tgz"
  integrity sha1-qUN36WSpo3rDl22EjLXHZYM7hUg=

node-addon-api@^6.1.0:
  version "6.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/node-addon-api/-/node-addon-api-6.1.0.tgz"
  integrity sha1-rIRwA05Y5n0MbxIEoYrmmV2cDXY=

node-emoji@1.11.0:
  version "1.11.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/node-emoji/-/node-emoji-1.11.0.tgz"
  integrity sha1-aaAVDmlG4vEV6dfqTfeXHiYoMBw=
  dependencies:
    lodash "^4.17.21"

node-fetch@^2.6.1:
  version "2.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/node-fetch/-/node-fetch-2.7.0.tgz"
  integrity sha1-0PD6bj4twdJ+/NitmdVQvalNGH0=
  dependencies:
    whatwg-url "^5.0.0"

node-gyp-build@^3.9.0, node-gyp-build@<4.0:
  version "3.9.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/node-gyp-build/-/node-gyp-build-3.9.0.tgz"
  integrity sha1-U6NQGH3U1SdnUNohYF0ctoHQniU=

node-gyp-build@^4.5.0:
  version "4.8.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/node-gyp-build/-/node-gyp-build-4.8.4.tgz"
  integrity sha1-inDuhUZK5SMndyqQ1mxgd6kAz8g=

node-int64@^0.4.0:
  version "0.4.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/node-int64/-/node-int64-0.4.0.tgz"
  integrity sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs=

node-releases@^2.0.19:
  version "2.0.19"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/node-releases/-/node-releases-2.0.19.tgz"
  integrity sha1-nkRaUpUJUexNF32EOvNwtBHK8xQ=

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/normalize-path/-/normalize-path-3.0.0.tgz"
  integrity sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=

npm-run-path@^4.0.1:
  version "4.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/npm-run-path/-/npm-run-path-4.0.1.tgz"
  integrity sha1-t+zR5e1T2o43pV4cImnguX7XSOo=
  dependencies:
    path-key "^3.0.0"

object-assign@^4, object-assign@^4.1.1:
  version "4.1.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/object-assign/-/object-assign-4.1.1.tgz"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-inspect@^1.13.3:
  version "1.13.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/object-inspect/-/object-inspect-1.13.4.tgz"
  integrity sha1-g3UmXiG8IND6WCwi4bE0hdbgAhM=

on-finished@^2.4.1, on-finished@2.4.1:
  version "2.4.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/on-finished/-/on-finished-2.4.1.tgz"
  integrity sha1-WMjEQRblSEWtV/FKsQsDUzGErD8=
  dependencies:
    ee-first "1.1.1"

on-headers@~1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/on-headers/-/on-headers-1.1.0.tgz"
  integrity sha1-WdpPkcRfX5icbkvO3Fo7Cu1w/2U=

once@^1.3.0, once@^1.4.0:
  version "1.4.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/once/-/once-1.4.0.tgz"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onetime@^5.1.0, onetime@^5.1.2:
  version "5.1.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/onetime/-/onetime-5.1.2.tgz"
  integrity sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=
  dependencies:
    mimic-fn "^2.1.0"

open@^10.1.0:
  version "10.2.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/open/-/open-10.2.0.tgz"
  integrity sha1-udhVvgB2IOgLb7BfrJgUH+Yttzw=
  dependencies:
    default-browser "^5.2.1"
    define-lazy-prop "^3.0.0"
    is-inside-container "^1.0.0"
    wsl-utils "^0.1.0"

opentracing@>=0.14.7:
  version "0.14.7"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/opentracing/-/opentracing-0.14.7.tgz"
  integrity sha1-JdRyvQKW3Atk17lMvJlSGQMUKPU=

optionator@^0.9.3:
  version "0.9.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/optionator/-/optionator-0.9.4.tgz"
  integrity sha1-fqHBpdkddk+yghOciP4R4YKjpzQ=
  dependencies:
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"
    word-wrap "^1.2.5"

ora@^5.4.1, ora@5.4.1:
  version "5.4.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ora/-/ora-5.4.1.tgz"
  integrity sha1-GyZ4Qmr0rEpQkAjl5KyemVnbnhg=
  dependencies:
    bl "^4.1.0"
    chalk "^4.1.0"
    cli-cursor "^3.1.0"
    cli-spinners "^2.5.0"
    is-interactive "^1.0.0"
    is-unicode-supported "^0.1.0"
    log-symbols "^4.1.0"
    strip-ansi "^6.0.0"
    wcwidth "^1.0.1"

os-tmpdir@~1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/os-tmpdir/-/os-tmpdir-1.0.2.tgz"
  integrity sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=

p-limit@^2.2.0:
  version "2.3.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/p-limit/-/p-limit-2.3.0.tgz"
  integrity sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=
  dependencies:
    p-try "^2.0.0"

p-limit@^3.0.2, p-limit@^3.1.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/p-limit/-/p-limit-3.1.0.tgz"
  integrity sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=
  dependencies:
    yocto-queue "^0.1.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/p-locate/-/p-locate-4.1.0.tgz"
  integrity sha1-o0KLtwiLOmApL2aRkni3wpetTwc=
  dependencies:
    p-limit "^2.2.0"

p-locate@^5.0.0:
  version "5.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/p-locate/-/p-locate-5.0.0.tgz"
  integrity sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ=
  dependencies:
    p-limit "^3.0.2"

p-try@^2.0.0:
  version "2.2.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/p-try/-/p-try-2.2.0.tgz"
  integrity sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=

package-json-from-dist@^1.0.0:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/package-json-from-dist/-/package-json-from-dist-1.0.1.tgz"
  integrity sha1-TxRxoBCCeob5TP2bByfjbSZ95QU=

parent-module@^1.0.0:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/parent-module/-/parent-module-1.0.1.tgz"
  integrity sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=
  dependencies:
    callsites "^3.0.0"

parse-json@^5.2.0:
  version "5.2.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/parse-json/-/parse-json-5.2.0.tgz"
  integrity sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

parseurl@^1.3.3, parseurl@~1.3.3:
  version "1.3.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/parseurl/-/parseurl-1.3.3.tgz"
  integrity sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=

passport-jwt@^4.0.1:
  version "4.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/passport-jwt/-/passport-jwt-4.0.1.tgz"
  integrity sha1-xEN5Xv8yLDjRc/qgo8SBR5ZG7D0=
  dependencies:
    jsonwebtoken "^9.0.0"
    passport-strategy "^1.0.0"

passport-strategy@^1.0.0, passport-strategy@1.x.x:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/passport-strategy/-/passport-strategy-1.0.0.tgz"
  integrity sha1-tVOaqPwiWj0a0XlHbd8ja0QPUuQ=

passport@^0.7.0:
  version "0.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/passport/-/passport-0.7.0.tgz"
  integrity sha1-NohBWlmkjPgGhBeoqAktRJLKOgU=
  dependencies:
    passport-strategy "1.x.x"
    pause "0.0.1"
    utils-merge "^1.0.1"

path-exists@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/path-exists/-/path-exists-4.0.0.tgz"
  integrity sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/path-key/-/path-key-3.1.1.tgz"
  integrity sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=

path-parse@^1.0.7:
  version "1.0.7"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/path-parse/-/path-parse-1.0.7.tgz"
  integrity sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=

path-scurry@^1.11.1:
  version "1.11.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/path-scurry/-/path-scurry-1.11.1.tgz"
  integrity sha1-eWCmaIiFlKByCxKpEdGnQqufEdI=
  dependencies:
    lru-cache "^10.2.0"
    minipass "^5.0.0 || ^6.0.2 || ^7.0.0"

path-to-regexp@^0.1.12:
  version "0.1.12"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/path-to-regexp/-/path-to-regexp-0.1.12.tgz"
  integrity sha1-1eGhLkeKl21DLvPFjVNLmSMWS7c=

path-to-regexp@^8.0.0:
  version "8.2.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/path-to-regexp/-/path-to-regexp-8.2.0.tgz"
  integrity sha1-c5kMwp5Xo/8qDZFAlRVt9dt56LQ=

path-to-regexp@3.3.0:
  version "3.3.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/path-to-regexp/-/path-to-regexp-3.3.0.tgz"
  integrity sha1-9/MdMuhRjCZghitkRBS21cY6YRs=

path-to-regexp@8.2.0:
  version "8.2.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/path-to-regexp/-/path-to-regexp-8.2.0.tgz"
  integrity sha1-c5kMwp5Xo/8qDZFAlRVt9dt56LQ=

path-type@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/path-type/-/path-type-4.0.0.tgz"
  integrity sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=

pause@0.0.1:
  version "0.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/pause/-/pause-0.0.1.tgz"
  integrity sha1-HUCLP9t2kjuVQ9lvtMnf1TXZy10=

picocolors@^1.1.1:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/picocolors/-/picocolors-1.1.1.tgz"
  integrity sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s=

picomatch@^2.0.4:
  version "2.3.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/picomatch/-/picomatch-2.3.1.tgz"
  integrity sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=

picomatch@^2.2.1:
  version "2.3.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/picomatch/-/picomatch-2.3.1.tgz"
  integrity sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=

picomatch@^2.2.3:
  version "2.3.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/picomatch/-/picomatch-2.3.1.tgz"
  integrity sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=

picomatch@^2.3.1:
  version "2.3.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/picomatch/-/picomatch-2.3.1.tgz"
  integrity sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=

picomatch@4.0.1:
  version "4.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/picomatch/-/picomatch-4.0.1.tgz"
  integrity sha1-aMJsiDc5nlgZ7c5IWQQS6gfxegc=

pirates@^4.0.4:
  version "4.0.7"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/pirates/-/pirates-4.0.7.tgz"
  integrity sha1-ZDtKGMQlfIplEEtz8wSc6aChXiI=

pkg-dir@^4.2.0:
  version "4.2.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/pkg-dir/-/pkg-dir-4.2.0.tgz"
  integrity sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=
  dependencies:
    find-up "^4.0.0"

pluralize@8.0.0:
  version "8.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/pluralize/-/pluralize-8.0.0.tgz"
  integrity sha1-Gm+hajjRKhkB4DIPoBcFHFOc47E=

possible-typed-array-names@^1.0.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/possible-typed-array-names/-/possible-typed-array-names-1.1.0.tgz"
  integrity sha1-k+NYK8DlQmWG2dB7ee5A/IQd5K4=

pprof-format@^2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/pprof-format/-/pprof-format-2.1.0.tgz"
  integrity sha1-rMjXdzvPT68KPT3xG87vunrAZmQ=

prelude-ls@^1.2.1:
  version "1.2.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/prelude-ls/-/prelude-ls-1.2.1.tgz"
  integrity sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=

prettier-linter-helpers@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz"
  integrity sha1-0j1B/hN1ZG3i0BBNNFSjAIgCz3s=
  dependencies:
    fast-diff "^1.1.2"

prettier@^3.0.0:
  version "3.6.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/prettier/-/prettier-3.6.2.tgz"
  integrity sha1-zNoCoQA+u7K/2m+DoHSXj2CLk5M=

pretty-format@^29.0.0, pretty-format@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/pretty-format/-/pretty-format-29.7.0.tgz"
  integrity sha1-ykLHWDEPNlv6caC9oKgHFgt3aBI=
  dependencies:
    "@jest/schemas" "^29.6.3"
    ansi-styles "^5.0.0"
    react-is "^18.0.0"

process@^0.11.10:
  version "0.11.10"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/process/-/process-0.11.10.tgz"
  integrity sha1-czIwDoQBYb2j5podHZGn1LwW8YI=

prompts@^2.0.1:
  version "2.4.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/prompts/-/prompts-2.4.2.tgz"
  integrity sha1-e1fnOzpIAprRDr1E90sBcipMsGk=
  dependencies:
    kleur "^3.0.3"
    sisteransi "^1.0.5"

protobufjs@^7.5.3:
  version "7.5.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/protobufjs/-/protobufjs-7.5.3.tgz"
  integrity sha1-E/lanjyEZpmV7DZS2yrC+wC4k2M=
  dependencies:
    "@protobufjs/aspromise" "^1.1.2"
    "@protobufjs/base64" "^1.1.2"
    "@protobufjs/codegen" "^2.0.4"
    "@protobufjs/eventemitter" "^1.1.0"
    "@protobufjs/fetch" "^1.1.0"
    "@protobufjs/float" "^1.0.2"
    "@protobufjs/inquire" "^1.1.0"
    "@protobufjs/path" "^1.1.2"
    "@protobufjs/pool" "^1.1.0"
    "@protobufjs/utf8" "^1.1.0"
    "@types/node" ">=13.7.0"
    long "^5.0.0"

proxy-addr@^2.0.7:
  version "2.0.7"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/proxy-addr/-/proxy-addr-2.0.7.tgz"
  integrity sha1-8Z/mnOqzEe65S0LnDowgcPm6ECU=
  dependencies:
    forwarded "0.2.0"
    ipaddr.js "1.9.1"

punycode@^2.1.0:
  version "2.3.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/punycode/-/punycode-2.3.1.tgz"
  integrity sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=

pure-rand@^6.0.0:
  version "6.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/pure-rand/-/pure-rand-6.1.0.tgz"
  integrity sha1-0XPPIyWCMZdsy9sFJHyXh5V2BPI=

qs@^6.11.2, qs@6.13.0:
  version "6.13.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/qs/-/qs-6.13.0.tgz"
  integrity sha1-bKO9WEOffiRWVXmJl3h7DYilGQY=
  dependencies:
    side-channel "^1.0.6"

qs@^6.14.0:
  version "6.14.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/qs/-/qs-6.14.0.tgz"
  integrity sha1-xj+kBoDSxclBQSoOiZyJr2DAqTA=
  dependencies:
    side-channel "^1.1.0"

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/queue-microtask/-/queue-microtask-1.2.3.tgz"
  integrity sha1-SSkii7xyTfrEPg77BYyve2z7YkM=

queue-tick@^1.0.0:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/queue-tick/-/queue-tick-1.0.1.tgz"
  integrity sha1-9vB6yCwf1g+C4Ji0F6gOUvH0wUI=

random-bytes@~1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/random-bytes/-/random-bytes-1.0.0.tgz"
  integrity sha1-T2ih3Arli9P7lYSMMDJNt11kNgs=

randombytes@^2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/randombytes/-/randombytes-2.1.0.tgz"
  integrity sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=
  dependencies:
    safe-buffer "^5.1.0"

range-parser@^1.2.1:
  version "1.2.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/range-parser/-/range-parser-1.2.1.tgz"
  integrity sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=

raw-body@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/raw-body/-/raw-body-3.0.0.tgz"
  integrity sha1-JbNHbwelFgBhna4/6C3cKKNuXg8=
  dependencies:
    bytes "3.1.2"
    http-errors "2.0.0"
    iconv-lite "0.6.3"
    unpipe "1.0.0"

raw-body@2.5.2:
  version "2.5.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/raw-body/-/raw-body-2.5.2.tgz"
  integrity sha1-mf69g7kOCJdQh+jx+UGaFJNmtoo=
  dependencies:
    bytes "3.1.2"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

react-is@^18.0.0:
  version "18.3.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/react-is/-/react-is-18.3.1.tgz"
  integrity sha1-6DVX3BLq5jqZ4AOkY4ix3LtE234=

readable-stream@^3.0.2, readable-stream@^3.4.0:
  version "3.6.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/readable-stream/-/readable-stream-3.6.2.tgz"
  integrity sha1-VqmzbqllwAxak+8x6xEaDxEFaWc=
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readable-stream@^4.2.0:
  version "4.7.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/readable-stream/-/readable-stream-4.7.0.tgz"
  integrity sha1-ztvYoRRsE9//jasUBoAo1YwVrJE=
  dependencies:
    abort-controller "^3.0.0"
    buffer "^6.0.3"
    events "^3.3.0"
    process "^0.11.10"
    string_decoder "^1.3.0"

readdirp@~3.6.0:
  version "3.6.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/readdirp/-/readdirp-3.6.0.tgz"
  integrity sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=
  dependencies:
    picomatch "^2.2.1"

redis@^4.7.0:
  version "4.7.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/redis/-/redis-4.7.1.tgz"
  integrity sha1-CFiKMJNr4OetnA8+GsaoXMr3PpQ=
  dependencies:
    "@redis/bloom" "1.2.0"
    "@redis/client" "1.6.1"
    "@redis/graph" "1.1.1"
    "@redis/json" "1.0.7"
    "@redis/search" "1.2.0"
    "@redis/time-series" "1.1.0"

redis@^5.8.0:
  version "5.8.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/redis/-/redis-5.8.0.tgz"
  integrity sha1-kP/ME0jLTmoBhG2UC+qMIob/I/Y=
  dependencies:
    "@redis/bloom" "5.8.0"
    "@redis/client" "5.8.0"
    "@redis/json" "5.8.0"
    "@redis/search" "5.8.0"
    "@redis/time-series" "5.8.0"

reflect-metadata@^0.2.0:
  version "0.2.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/reflect-metadata/-/reflect-metadata-0.2.2.tgz"
  integrity sha1-QAyEW2y6h6IfLGXErrFY9PpNnFs=

repeat-string@^1.6.1:
  version "1.6.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/repeat-string/-/repeat-string-1.6.1.tgz"
  integrity sha1-jcrkcOHIirwtYA//Sndihtp15jc=

require-directory@^2.1.1:
  version "2.1.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/require-directory/-/require-directory-2.1.1.tgz"
  integrity sha1-jGStX9MNqxyXbiNE/+f3kqam30I=

require-from-string@^2.0.2:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/require-from-string/-/require-from-string-2.0.2.tgz"
  integrity sha1-iaf92TgmEmcxjq/hT5wy5ZjDaQk=

resolve-cwd@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/resolve-cwd/-/resolve-cwd-3.0.0.tgz"
  integrity sha1-DwB18bslRHZs9zumpuKt/ryxPy0=
  dependencies:
    resolve-from "^5.0.0"

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/resolve-from/-/resolve-from-4.0.0.tgz"
  integrity sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=

resolve-from@^5.0.0:
  version "5.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/resolve-from/-/resolve-from-5.0.0.tgz"
  integrity sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=

resolve.exports@^2.0.0:
  version "2.0.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/resolve.exports/-/resolve.exports-2.0.3.tgz"
  integrity sha1-QZVebxtAE7dYb4c3SaY13qB+vj8=

resolve@^1.20.0:
  version "1.22.10"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/resolve/-/resolve-1.22.10.tgz"
  integrity sha1-tmPoP/sJu/I4aURza6roAwKbizk=
  dependencies:
    is-core-module "^2.16.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

restore-cursor@^3.1.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/restore-cursor/-/restore-cursor-3.1.0.tgz"
  integrity sha1-OfZ8VLOnpYzqUjbZXPADQjljH34=
  dependencies:
    onetime "^5.1.0"
    signal-exit "^3.0.2"

retry@^0.13.1:
  version "0.13.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/retry/-/retry-0.13.1.tgz"
  integrity sha1-GFsVh6z2eRnWOzVzSeA1N7JIRlg=

reusify@^1.0.4:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/reusify/-/reusify-1.1.0.tgz"
  integrity sha1-D+E7lSLhRz9RtVjueW4I8R+bSJ8=

rfdc@^1.3.0, rfdc@^1.4.1:
  version "1.4.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/rfdc/-/rfdc-1.4.1.tgz"
  integrity sha1-d492xPtzHZNBTo+SX77PZMzn9so=

rimraf@^3.0.2:
  version "3.0.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/rimraf/-/rimraf-3.0.2.tgz"
  integrity sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=
  dependencies:
    glob "^7.1.3"

router@^2.2.0:
  version "2.2.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/router/-/router-2.2.0.tgz"
  integrity sha1-AZvmILcRyHZBFnzHm5kJDwCxRu8=
  dependencies:
    debug "^4.4.0"
    depd "^2.0.0"
    is-promise "^4.0.0"
    parseurl "^1.3.3"
    path-to-regexp "^8.0.0"

run-applescript@^7.0.0:
  version "7.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/run-applescript/-/run-applescript-7.0.0.tgz"
  integrity sha1-5aVTwr/9Yg4WnSdsHNjxtkd4++s=

run-async@^2.4.0:
  version "2.4.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/run-async/-/run-async-2.4.1.tgz"
  integrity sha1-hEDsz5nqPnC9QJ1JqriOEMGJpFU=

run-async@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/run-async/-/run-async-3.0.0.tgz"
  integrity sha1-QqQy9tdsaJUiBYmEOE3yi+N52q0=

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/run-parallel/-/run-parallel-1.2.0.tgz"
  integrity sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=
  dependencies:
    queue-microtask "^1.2.2"

rxjs@^7.5.5, rxjs@^7.8.1:
  version "7.8.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/rxjs/-/rxjs-7.8.2.tgz"
  integrity sha1-lVvEc+2K8RoAKivlIHG/R1Y4YHs=
  dependencies:
    tslib "^2.1.0"

rxjs@7.8.1:
  version "7.8.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/rxjs/-/rxjs-7.8.1.tgz"
  integrity sha1-b289meqARCke/ZLnx/z1YsQFdUM=
  dependencies:
    tslib "^2.1.0"

safe-buffer@^5.0.1, safe-buffer@^5.1.0, safe-buffer@^5.2.1, safe-buffer@~5.2.0, safe-buffer@5.2.1:
  version "5.2.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/safe-buffer/-/safe-buffer-5.2.1.tgz"
  integrity sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=

"safer-buffer@>= 2.1.2 < 3", "safer-buffer@>= 2.1.2 < 3.0.0":
  version "2.1.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/safer-buffer/-/safer-buffer-2.1.2.tgz"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

schema-utils@^3.1.1, schema-utils@^3.2.0:
  version "3.3.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/schema-utils/-/schema-utils-3.3.0.tgz"
  integrity sha1-9QqIh3w8AWUqFbYirp6Xld96YP4=
  dependencies:
    "@types/json-schema" "^7.0.8"
    ajv "^6.12.5"
    ajv-keywords "^3.5.2"

schema-utils@^4.3.0:
  version "4.3.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/schema-utils/-/schema-utils-4.3.2.tgz"
  integrity sha1-DBCHi/SnP9Kx39FLlGKyZ4jIBq4=
  dependencies:
    "@types/json-schema" "^7.0.9"
    ajv "^8.9.0"
    ajv-formats "^2.1.1"
    ajv-keywords "^5.1.0"

semifies@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/semifies/-/semifies-1.0.0.tgz"
  integrity sha1-tpVp8ywroqwE9wXqgoMTZCibKuI=

semver@^6.3.0:
  version "6.3.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/semver/-/semver-6.3.1.tgz"
  integrity sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=

semver@^6.3.1:
  version "6.3.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/semver/-/semver-6.3.1.tgz"
  integrity sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=

semver@^7.3.4, semver@^7.3.5, semver@^7.5.3, semver@^7.5.4, semver@^7.6.0, semver@^7.7.2:
  version "7.7.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/semver/-/semver-7.7.2.tgz"
  integrity sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=

send@^1.1.0, send@^1.2.0:
  version "1.2.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/send/-/send-1.2.0.tgz"
  integrity sha1-MqdVT7d3uDHfqCg3D3c6OAjTchI=
  dependencies:
    debug "^4.3.5"
    encodeurl "^2.0.0"
    escape-html "^1.0.3"
    etag "^1.8.1"
    fresh "^2.0.0"
    http-errors "^2.0.0"
    mime-types "^3.0.1"
    ms "^2.1.3"
    on-finished "^2.4.1"
    range-parser "^1.2.1"
    statuses "^2.0.1"

serialize-javascript@^6.0.2:
  version "6.0.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/serialize-javascript/-/serialize-javascript-6.0.2.tgz"
  integrity sha1-3voeBVyDv21Z6oBdjahiJU62psI=
  dependencies:
    randombytes "^2.1.0"

serve-static@^2.2.0:
  version "2.2.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/serve-static/-/serve-static-2.2.0.tgz"
  integrity sha1-nAJWTuJZvdIlG4LWWaLn4ZONZvk=
  dependencies:
    encodeurl "^2.0.0"
    escape-html "^1.0.3"
    parseurl "^1.3.3"
    send "^1.2.0"

set-function-length@^1.2.2:
  version "1.2.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/set-function-length/-/set-function-length-1.2.2.tgz"
  integrity sha1-qscjFBmOrtl1z3eyw7a4gGleVEk=
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"

setprototypeof@1.2.0:
  version "1.2.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/setprototypeof/-/setprototypeof-1.2.0.tgz"
  integrity sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ=

sha.js@^2.4.11:
  version "2.4.12"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/sha.js/-/sha.js-2.4.12.tgz"
  integrity sha1-64tWi/OD39GGejLD8rdOtSvb8j8=
  dependencies:
    inherits "^2.0.4"
    safe-buffer "^5.2.1"
    to-buffer "^1.2.0"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/shebang-command/-/shebang-command-2.0.0.tgz"
  integrity sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/shebang-regex/-/shebang-regex-3.0.0.tgz"
  integrity sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=

shell-quote@^1.8.2:
  version "1.8.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/shell-quote/-/shell-quote-1.8.3.tgz"
  integrity sha1-VeQO8zz1xomQI1Oj2M0aZyXwi0s=

side-channel-list@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/side-channel-list/-/side-channel-list-1.0.0.tgz"
  integrity sha1-EMtZhCYxFdO3oOM2WR4pCoMK+K0=
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"

side-channel-map@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/side-channel-map/-/side-channel-map-1.0.1.tgz"
  integrity sha1-1rtrN5Asb+9RdOX1M/q0xzKib0I=
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"

side-channel-weakmap@^1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz"
  integrity sha1-Ed2hnVNo5Azp7CvcH7DsvAeQ7Oo=
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"
    side-channel-map "^1.0.1"

side-channel@^1.0.6, side-channel@^1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/side-channel/-/side-channel-1.1.0.tgz"
  integrity sha1-w/z/nE2pMnhIczNeyXZfqU/2a8k=
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"
    side-channel-list "^1.0.0"
    side-channel-map "^1.0.1"
    side-channel-weakmap "^1.0.2"

signal-exit@^3.0.2:
  version "3.0.7"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/signal-exit/-/signal-exit-3.0.7.tgz"
  integrity sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk=

signal-exit@^3.0.3:
  version "3.0.7"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/signal-exit/-/signal-exit-3.0.7.tgz"
  integrity sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk=

signal-exit@^3.0.7:
  version "3.0.7"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/signal-exit/-/signal-exit-3.0.7.tgz"
  integrity sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk=

signal-exit@^4.0.1:
  version "4.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/signal-exit/-/signal-exit-4.1.0.tgz"
  integrity sha1-lSGIwcvVRgcOLdIND0HArgUwywQ=

sisteransi@^1.0.5:
  version "1.0.5"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/sisteransi/-/sisteransi-1.0.5.tgz"
  integrity sha1-E01oEpd1ZDfMBcoBNw06elcQde0=

slash@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/slash/-/slash-3.0.0.tgz"
  integrity sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=

source-map-support@^0.5.21, source-map-support@~0.5.20:
  version "0.5.21"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/source-map-support/-/source-map-support-0.5.21.tgz"
  integrity sha1-BP58f54e0tZiIzwoyys1ufY/bk8=
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map-support@0.5.13:
  version "0.5.13"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/source-map-support/-/source-map-support-0.5.13.tgz"
  integrity sha1-MbJKnC5zwt6FBmwP631Edn7VKTI=
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map@^0.6.0:
  version "0.6.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/source-map/-/source-map-0.6.1.tgz"
  integrity sha1-dHIq8y6WFOnCh6jQu95IteLxomM=

source-map@^0.6.1:
  version "0.6.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/source-map/-/source-map-0.6.1.tgz"
  integrity sha1-dHIq8y6WFOnCh6jQu95IteLxomM=

source-map@^0.7.4, source-map@0.7.4:
  version "0.7.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/source-map/-/source-map-0.7.4.tgz"
  integrity sha1-qbvnBcnYhG9OCP9nZazw8bCJhlY=

sprintf-js@^1.1.3:
  version "1.1.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/sprintf-js/-/sprintf-js-1.1.3.tgz"
  integrity sha1-SRS5A6L4toXRf994pw6RfocuREo=

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/sprintf-js/-/sprintf-js-1.0.3.tgz"
  integrity sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=

sql-highlight@^6.0.0:
  version "6.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/sql-highlight/-/sql-highlight-6.1.0.tgz"
  integrity sha1-40AktMbqwnRGSHce3+PB+JQVN0M=

stack-utils@^2.0.3:
  version "2.0.6"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/stack-utils/-/stack-utils-2.0.6.tgz"
  integrity sha1-qvB0gWnAL8M8gjKrzPkz9Uocw08=
  dependencies:
    escape-string-regexp "^2.0.0"

statuses@^2.0.1:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/statuses/-/statuses-2.0.2.tgz"
  integrity sha1-j3XuzvdlteHPzcCA2llAntQk44I=

statuses@2.0.1:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/statuses/-/statuses-2.0.1.tgz"
  integrity sha1-VcsADM8dSHKL0jxoWgY5mM8aG2M=

streamsearch@^1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/streamsearch/-/streamsearch-1.1.0.tgz"
  integrity sha1-QE3R4iR8qUr1VOhBqO8OqiONp2Q=

string_decoder@^1.1.1, string_decoder@^1.3.0:
  version "1.3.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/string_decoder/-/string_decoder-1.3.0.tgz"
  integrity sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=
  dependencies:
    safe-buffer "~5.2.0"

string-length@^4.0.1:
  version "4.0.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/string-length/-/string-length-4.0.2.tgz"
  integrity sha1-qKjce9XBqCubPIuH4SX2aHG25Xo=
  dependencies:
    char-regex "^1.0.2"
    strip-ansi "^6.0.0"

"string-width-cjs@npm:string-width@^4.2.0":
  version "4.2.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/string-width/-/string-width-4.2.3.tgz"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^4.1.0, string-width@^4.2.0, string-width@^4.2.3:
  version "4.2.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/string-width/-/string-width-4.2.3.tgz"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^5.0.1, string-width@^5.1.2:
  version "5.1.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/string-width/-/string-width-5.1.2.tgz"
  integrity sha1-FPja7G2B5yIdKjV+Zoyrc728p5Q=
  dependencies:
    eastasianwidth "^0.2.0"
    emoji-regex "^9.2.2"
    strip-ansi "^7.0.1"

"strip-ansi-cjs@npm:strip-ansi@^6.0.1":
  version "6.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/strip-ansi/-/strip-ansi-6.0.1.tgz"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/strip-ansi/-/strip-ansi-6.0.1.tgz"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^7.0.1:
  version "7.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/strip-ansi/-/strip-ansi-7.1.0.tgz"
  integrity sha1-1bZWjKaJ2FYTcLBwdoXSJDT6/0U=
  dependencies:
    ansi-regex "^6.0.1"

strip-bom@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/strip-bom/-/strip-bom-3.0.0.tgz"
  integrity sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=

strip-bom@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/strip-bom/-/strip-bom-4.0.0.tgz"
  integrity sha1-nDUFwdtFvO3KPZz3oW9cWqOQGHg=

strip-final-newline@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/strip-final-newline/-/strip-final-newline-2.0.0.tgz"
  integrity sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=

strip-json-comments@^3.1.1:
  version "3.1.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/strip-json-comments/-/strip-json-comments-3.1.1.tgz"
  integrity sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=

strtok3@^10.2.2:
  version "10.3.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/strtok3/-/strtok3-10.3.4.tgz"
  integrity sha1-eT69DVnfJ2oIVYYTS3OkBuYL6cE=
  dependencies:
    "@tokenizer/token" "^0.3.0"

superagent@^10.2.3:
  version "10.2.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/superagent/-/superagent-10.2.3.tgz"
  integrity sha1-0eSYbyyqxCPDfjgHf5BzzP5zpZs=
  dependencies:
    component-emitter "^1.3.1"
    cookiejar "^2.1.4"
    debug "^4.3.7"
    fast-safe-stringify "^2.1.1"
    form-data "^4.0.4"
    formidable "^3.5.4"
    methods "^1.1.2"
    mime "2.6.0"
    qs "^6.11.2"

supertest@^7.0.0:
  version "7.1.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/supertest/-/supertest-7.1.4.tgz"
  integrity sha1-MXXiU59RfKcv3HmS//81uUrKfTQ=
  dependencies:
    methods "^1.1.2"
    superagent "^10.2.3"

supports-color@^7.1.0:
  version "7.2.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/supports-color/-/supports-color-7.2.0.tgz"
  integrity sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=
  dependencies:
    has-flag "^4.0.0"

supports-color@^8.0.0:
  version "8.1.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/supports-color/-/supports-color-8.1.1.tgz"
  integrity sha1-zW/BfihQDP9WwbhsCn/UpUpzAFw=
  dependencies:
    has-flag "^4.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
  integrity sha1-btpL00SjyUrqN21MwxvHcxEDngk=

swagger-ui-dist@5.18.2:
  version "5.18.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/swagger-ui-dist/-/swagger-ui-dist-5.18.2.tgz"
  integrity sha1-YgEwdDdNJywE7TAwcEuI21qowLc=
  dependencies:
    "@scarf/scarf" "=1.4.0"

symbol-observable@4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/symbol-observable/-/symbol-observable-4.0.0.tgz"
  integrity sha1-W0JfGSJ56H8vm5N6yFQNGYSzkgU=

synckit@^0.11.7:
  version "0.11.11"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/synckit/-/synckit-0.11.11.tgz"
  integrity sha1-wLYZzyWKl/qiCRVdnNFpm1yZjLA=
  dependencies:
    "@pkgr/core" "^0.2.9"

tapable@^2.1.1, tapable@^2.2.0, tapable@^2.2.1:
  version "2.2.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/tapable/-/tapable-2.2.2.tgz"
  integrity sha1-q0mENA0wy5mJpJADLwhtu4tW2HI=

tarn@^3.0.2:
  version "3.0.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/tarn/-/tarn-3.0.2.tgz"
  integrity sha1-c7YUD7uIG3FVnE+L/ePZpLPSdpM=

tedious@^18.2.1:
  version "18.6.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/tedious/-/tedious-18.6.1.tgz"
  integrity sha1-HEo/BsiRvmegMhF+LiUZMobURJY=
  dependencies:
    "@azure/core-auth" "^1.7.2"
    "@azure/identity" "^4.2.1"
    "@azure/keyvault-keys" "^4.4.0"
    "@js-joda/core" "^5.6.1"
    "@types/node" ">=18"
    bl "^6.0.11"
    iconv-lite "^0.6.3"
    js-md4 "^0.3.2"
    native-duplexpair "^1.0.0"
    sprintf-js "^1.1.3"

terser-webpack-plugin@^5.3.10:
  version "5.3.14"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/terser-webpack-plugin/-/terser-webpack-plugin-5.3.14.tgz"
  integrity sha1-kDHUjlerJ1Z/AqzoXH1pDbZsPgY=
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.25"
    jest-worker "^27.4.5"
    schema-utils "^4.3.0"
    serialize-javascript "^6.0.2"
    terser "^5.31.1"

terser@^5.31.1:
  version "5.43.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/terser/-/terser-5.43.1.tgz"
  integrity sha1-iDh/T5eU/xop561h+yky4ltP220=
  dependencies:
    "@jridgewell/source-map" "^0.3.3"
    acorn "^8.14.0"
    commander "^2.20.0"
    source-map-support "~0.5.20"

test-exclude@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/test-exclude/-/test-exclude-6.0.0.tgz"
  integrity sha1-BKhphmHYBepvopO2y55jrARO8V4=
  dependencies:
    "@istanbuljs/schema" "^0.1.2"
    glob "^7.1.4"
    minimatch "^3.0.4"

text-table@^0.2.0:
  version "0.2.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/text-table/-/text-table-0.2.0.tgz"
  integrity sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=

through@^2.3.6:
  version "2.3.8"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/through/-/through-2.3.8.tgz"
  integrity sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=

tlhunter-sorted-set@^0.1.0:
  version "0.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/tlhunter-sorted-set/-/tlhunter-sorted-set-0.1.0.tgz"
  integrity sha1-HD6uKMD6Tf+X6VAdLjwgS4ZAb0s=

tmp@^0.0.33:
  version "0.0.33"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/tmp/-/tmp-0.0.33.tgz"
  integrity sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=
  dependencies:
    os-tmpdir "~1.0.2"

tmpl@1.0.5:
  version "1.0.5"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/tmpl/-/tmpl-1.0.5.tgz"
  integrity sha1-hoPguQK7nCDE9ybjwLafNlGMB8w=

to-buffer@^1.2.0:
  version "1.2.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/to-buffer/-/to-buffer-1.2.1.tgz"
  integrity sha1-LOZQzbJi6REqGOZdwp3LUTyBVeA=
  dependencies:
    isarray "^2.0.5"
    safe-buffer "^5.2.1"
    typed-array-buffer "^1.0.3"

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/to-regex-range/-/to-regex-range-5.0.1.tgz"
  integrity sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=
  dependencies:
    is-number "^7.0.0"

toidentifier@1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/toidentifier/-/toidentifier-1.0.1.tgz"
  integrity sha1-O+NDIaiKgg7RvYDfqjPkefu43TU=

token-types@^6.0.0:
  version "6.0.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/token-types/-/token-types-6.0.4.tgz"
  integrity sha1-FYLvbR13OYzXOMh+o43Vcp+knsU=
  dependencies:
    "@tokenizer/token" "^0.3.0"
    ieee754 "^1.2.1"

tr46@~0.0.3:
  version "0.0.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/tr46/-/tr46-0.0.3.tgz"
  integrity sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o=

tree-kill@1.2.2:
  version "1.2.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/tree-kill/-/tree-kill-1.2.2.tgz"
  integrity sha1-TKCakJLIi3OnzcXooBtQeweQoMw=

ts-api-utils@^1.3.0:
  version "1.4.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ts-api-utils/-/ts-api-utils-1.4.3.tgz"
  integrity sha1-v8IhX+ZSj+yrKw+6VwouikJjsGQ=

ts-jest@^29.1.0:
  version "29.4.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ts-jest/-/ts-jest-29.4.1.tgz"
  integrity sha1-QtM763Rld1HTFe+5qHH+meO5tRk=
  dependencies:
    bs-logger "^0.2.6"
    fast-json-stable-stringify "^2.1.0"
    handlebars "^4.7.8"
    json5 "^2.2.3"
    lodash.memoize "^4.1.2"
    make-error "^1.3.6"
    semver "^7.7.2"
    type-fest "^4.41.0"
    yargs-parser "^21.1.1"

ts-loader@^9.4.3:
  version "9.5.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ts-loader/-/ts-loader-9.5.2.tgz"
  integrity sha1-Hz1/S7cJtIeqomDo8ZswFjXQgCA=
  dependencies:
    chalk "^4.1.0"
    enhanced-resolve "^5.0.0"
    micromatch "^4.0.0"
    semver "^7.3.4"
    source-map "^0.7.4"

ts-node@^10.9.2:
  version "10.9.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ts-node/-/ts-node-10.9.2.tgz"
  integrity sha1-cPAhyeGFvM3Kgg4m3EE4BcEBxx8=
  dependencies:
    "@cspotcode/source-map-support" "^0.8.0"
    "@tsconfig/node10" "^1.0.7"
    "@tsconfig/node12" "^1.0.7"
    "@tsconfig/node14" "^1.0.0"
    "@tsconfig/node16" "^1.0.2"
    acorn "^8.4.1"
    acorn-walk "^8.1.1"
    arg "^4.1.0"
    create-require "^1.1.0"
    diff "^4.0.1"
    make-error "^1.1.1"
    v8-compile-cache-lib "^3.0.1"
    yn "3.1.1"

tsconfig-paths-webpack-plugin@4.2.0:
  version "4.2.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/tsconfig-paths-webpack-plugin/-/tsconfig-paths-webpack-plugin-4.2.0.tgz"
  integrity sha1-90WajtHdTPZq14eu/D03//PPB/w=
  dependencies:
    chalk "^4.1.0"
    enhanced-resolve "^5.7.0"
    tapable "^2.2.1"
    tsconfig-paths "^4.1.2"

tsconfig-paths@^4.1.2, tsconfig-paths@^4.2.0, tsconfig-paths@4.2.0:
  version "4.2.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/tsconfig-paths/-/tsconfig-paths-4.2.0.tgz"
  integrity sha1-73jhkDkTNEbSRL6sD9ahYy4tEHw=
  dependencies:
    json5 "^2.2.2"
    minimist "^1.2.6"
    strip-bom "^3.0.0"

tslib@^2.1.0, tslib@^2.2.0, tslib@^2.6.2, tslib@^2.8.1, tslib@2.8.1:
  version "2.8.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/tslib/-/tslib-2.8.1.tgz"
  integrity sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8=

ttl-set@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ttl-set/-/ttl-set-1.0.0.tgz"
  integrity sha1-54ldlGrZzt+tz24zhOqXMiqG3Ts=
  dependencies:
    fast-fifo "^1.3.2"

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/type-check/-/type-check-0.4.0.tgz"
  integrity sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=
  dependencies:
    prelude-ls "^1.2.1"

type-detect@4.0.8:
  version "4.0.8"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/type-detect/-/type-detect-4.0.8.tgz"
  integrity sha1-dkb7XxiHHPu3dJ5pvTmmOI63RQw=

type-fest@^0.20.2:
  version "0.20.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/type-fest/-/type-fest-0.20.2.tgz"
  integrity sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ=

type-fest@^0.21.3:
  version "0.21.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/type-fest/-/type-fest-0.21.3.tgz"
  integrity sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc=

type-fest@^4.41.0:
  version "4.41.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/type-fest/-/type-fest-4.41.0.tgz"
  integrity sha1-auHI5XMSc8K/H1itOcuuLJGkbFg=

type-is@^1.6.18, type-is@~1.6.18:
  version "1.6.18"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/type-is/-/type-is-1.6.18.tgz"
  integrity sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

type-is@^2.0.0, type-is@^2.0.1:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/type-is/-/type-is-2.0.1.tgz"
  integrity sha1-ZPbPA/kvzkAVwrIkeT9r3UsGjJc=
  dependencies:
    content-type "^1.0.5"
    media-typer "^1.1.0"
    mime-types "^3.0.0"

typed-array-buffer@^1.0.3:
  version "1.0.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/typed-array-buffer/-/typed-array-buffer-1.0.3.tgz"
  integrity sha1-pyOVRQpIaewDP9VJNxtHrzou5TY=
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    is-typed-array "^1.1.14"

typedarray@^0.0.6:
  version "0.0.6"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/typedarray/-/typedarray-0.0.6.tgz"
  integrity sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=

typeorm-naming-strategies@^4.1.0:
  version "4.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/typeorm-naming-strategies/-/typeorm-naming-strategies-4.1.0.tgz"
  integrity sha1-HsbrKWyNe2m7BnZNW5CD/4DoFKk=

typeorm@^0.3.24, typeorm@^0.3.25:
  version "0.3.25"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/typeorm/-/typeorm-0.3.25.tgz"
  integrity sha1-mkFvk82g9hKyD4RQ4D1rDhG0Z/s=
  dependencies:
    "@sqltools/formatter" "^1.2.5"
    ansis "^3.17.0"
    app-root-path "^3.1.0"
    buffer "^6.0.3"
    dayjs "^1.11.13"
    debug "^4.4.0"
    dedent "^1.6.0"
    dotenv "^16.4.7"
    glob "^10.4.5"
    sha.js "^2.4.11"
    sql-highlight "^6.0.0"
    tslib "^2.8.1"
    uuid "^11.1.0"
    yargs "^17.7.2"

typescript@^5.1.3:
  version "5.9.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/typescript/-/typescript-5.9.2.tgz"
  integrity sha1-2TRQzd7FFUotXKvjuBArgzFvsqY=

typescript@5.7.2:
  version "5.7.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/typescript/-/typescript-5.7.2.tgz"
  integrity sha1-MWnPjEyKgozeU7qeyz0rHV3We+Y=

uglify-js@^3.1.4:
  version "3.19.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/uglify-js/-/uglify-js-3.19.3.tgz"
  integrity sha1-gjFem7xvKyWIiFis0f/4RBA1t38=

uid-safe@~2.1.5:
  version "2.1.5"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/uid-safe/-/uid-safe-2.1.5.tgz"
  integrity sha1-Kz1cckDo/C5Y+Komnl7knAhXvTo=
  dependencies:
    random-bytes "~1.0.0"

uid@2.0.2:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/uid/-/uid-2.0.2.tgz"
  integrity sha1-S1eCq/Dy/u78APqIAGsrO3rz47k=
  dependencies:
    "@lukeed/csprng" "^1.0.0"

uint8array-extras@^1.4.0:
  version "1.4.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/uint8array-extras/-/uint8array-extras-1.4.0.tgz"
  integrity sha1-5Cpnim3TNewtIWYTM+1C9ErnzHQ=

undici-types@~6.21.0:
  version "6.21.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/undici-types/-/undici-types-6.21.0.tgz"
  integrity sha1-aR0ArzkJvpOn+qE75hs6W1DvEss=

universalify@^2.0.0:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/universalify/-/universalify-2.0.1.tgz"
  integrity sha1-Fo78IYCWTmOG0GHglN9hr+I5sY0=

unpipe@1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/unpipe/-/unpipe-1.0.0.tgz"
  integrity sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=

update-browserslist-db@^1.1.3:
  version "1.1.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz"
  integrity sha1-NIN33SRSFvnnBg/1CxWht0C3VCA=
  dependencies:
    escalade "^3.2.0"
    picocolors "^1.1.1"

uri-js@^4.2.2:
  version "4.4.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/uri-js/-/uri-js-4.4.1.tgz"
  integrity sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=
  dependencies:
    punycode "^2.1.0"

util-deprecate@^1.0.1:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/util-deprecate/-/util-deprecate-1.0.2.tgz"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

utils-merge@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/utils-merge/-/utils-merge-1.0.1.tgz"
  integrity sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=

uuid@^11.1.0:
  version "11.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/uuid/-/uuid-11.1.0.tgz"
  integrity sha1-lUkCi+F1O7k0/JbivKCbtBBa6RI=

uuid@^8.3.0:
  version "8.3.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/uuid/-/uuid-8.3.2.tgz"
  integrity sha1-gNW1ztJxu5r2xEXyGhoExgbO++I=

uuid@9.0.1:
  version "9.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/uuid/-/uuid-9.0.1.tgz"
  integrity sha1-4YjUyIU8xyIiA5LEJM1jfzIpPzA=

v8-compile-cache-lib@^3.0.1:
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/v8-compile-cache-lib/-/v8-compile-cache-lib-3.0.1.tgz"
  integrity sha1-Yzbo1xllyz01obu3hoRFp8BSZL8=

v8-to-istanbul@^9.0.1:
  version "9.3.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/v8-to-istanbul/-/v8-to-istanbul-9.3.0.tgz"
  integrity sha1-uVcqv6Yr1VbBbXX968GkEdX/MXU=
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.12"
    "@types/istanbul-lib-coverage" "^2.0.1"
    convert-source-map "^2.0.0"

validator@^13.9.0:
  version "13.15.15"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/validator/-/validator-13.15.15.tgz"
  integrity sha1-JGWUvlZx3Anao1yuxWifzRjG5+Q=

vary@^1, vary@^1.1.2, vary@~1.1.2:
  version "1.1.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/vary/-/vary-1.1.2.tgz"
  integrity sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=

walker@^1.0.8:
  version "1.0.8"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/walker/-/walker-1.0.8.tgz"
  integrity sha1-vUmNtHev5XPcBBhfAR06uKjXZT8=
  dependencies:
    makeerror "1.0.12"

watchpack@^2.4.1:
  version "2.4.4"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/watchpack/-/watchpack-2.4.4.tgz"
  integrity sha1-RzvacvCFBFPaZCUIHqRvwNdgKUc=
  dependencies:
    glob-to-regexp "^0.4.1"
    graceful-fs "^4.1.2"

wcwidth@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/wcwidth/-/wcwidth-1.0.1.tgz"
  integrity sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g=
  dependencies:
    defaults "^1.0.3"

webidl-conversions@^3.0.0:
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/webidl-conversions/-/webidl-conversions-3.0.1.tgz"
  integrity sha1-JFNCdeKnvGvnvIZhHMFq4KVlSHE=

webpack-node-externals@3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/webpack-node-externals/-/webpack-node-externals-3.0.0.tgz"
  integrity sha1-GjQHwVjVR6n+tCKanjOFt7YMmRc=

webpack-sources@^3.2.3:
  version "3.3.3"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/webpack-sources/-/webpack-sources-3.3.3.tgz"
  integrity sha1-1L9/mQlnXXoHD/FNDvKk88mCxyM=

webpack@5.97.1:
  version "5.97.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/webpack/-/webpack-5.97.1.tgz"
  integrity sha1-lyqDIKQ4tW/w8dlK3p6C6sFV+lg=
  dependencies:
    "@types/eslint-scope" "^3.7.7"
    "@types/estree" "^1.0.6"
    "@webassemblyjs/ast" "^1.14.1"
    "@webassemblyjs/wasm-edit" "^1.14.1"
    "@webassemblyjs/wasm-parser" "^1.14.1"
    acorn "^8.14.0"
    browserslist "^4.24.0"
    chrome-trace-event "^1.0.2"
    enhanced-resolve "^5.17.1"
    es-module-lexer "^1.2.1"
    eslint-scope "5.1.1"
    events "^3.2.0"
    glob-to-regexp "^0.4.1"
    graceful-fs "^4.2.11"
    json-parse-even-better-errors "^2.3.1"
    loader-runner "^4.2.0"
    mime-types "^2.1.27"
    neo-async "^2.6.2"
    schema-utils "^3.2.0"
    tapable "^2.1.1"
    terser-webpack-plugin "^5.3.10"
    watchpack "^2.4.1"
    webpack-sources "^3.2.3"

whatwg-url@^5.0.0:
  version "5.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/whatwg-url/-/whatwg-url-5.0.0.tgz"
  integrity sha1-lmRU6HZUYuN2RNNib2dCzotwll0=
  dependencies:
    tr46 "~0.0.3"
    webidl-conversions "^3.0.0"

which-typed-array@^1.1.16:
  version "1.1.19"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/which-typed-array/-/which-typed-array-1.1.19.tgz"
  integrity sha1-3wOELocLa4jhF1JKSzZLb8aJ+VY=
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    call-bound "^1.0.4"
    for-each "^0.3.5"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    has-tostringtag "^1.0.2"

which@^2.0.1:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/which/-/which-2.0.2.tgz"
  integrity sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=
  dependencies:
    isexe "^2.0.0"

word-wrap@^1.2.5:
  version "1.2.5"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/word-wrap/-/word-wrap-1.2.5.tgz"
  integrity sha1-0sRcbdT7zmIaZvE2y+Mor9BBCzQ=

wordwrap@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/wordwrap/-/wordwrap-1.0.0.tgz"
  integrity sha1-J1hIEIkUVqQXHI0CJkQa3pDLyus=

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  version "7.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  integrity sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^6.0.1, wrap-ansi@^6.2.0:
  version "6.2.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/wrap-ansi/-/wrap-ansi-6.2.0.tgz"
  integrity sha1-6Tk7oHEC5skaOyIUePAlfNKFblM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^7.0.0:
  version "7.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  integrity sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^8.1.0:
  version "8.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/wrap-ansi/-/wrap-ansi-8.1.0.tgz"
  integrity sha1-VtwiNo7lcPrOG0mBmXXZuaXq0hQ=
  dependencies:
    ansi-styles "^6.1.0"
    string-width "^5.0.1"
    strip-ansi "^7.0.1"

wrappy@1:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/wrappy/-/wrappy-1.0.2.tgz"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

write-file-atomic@^4.0.2:
  version "4.0.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/write-file-atomic/-/write-file-atomic-4.0.2.tgz"
  integrity sha1-qd8Brlt3hYoCf9LoB2juQzVV/P0=
  dependencies:
    imurmurhash "^0.1.4"
    signal-exit "^3.0.7"

wsl-utils@^0.1.0:
  version "0.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/wsl-utils/-/wsl-utils-0.1.0.tgz"
  integrity sha1-h4PU32cdTVA2W+LuTHGRegVXuqs=
  dependencies:
    is-wsl "^3.1.0"

xtend@^4.0.2:
  version "4.0.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/xtend/-/xtend-4.0.2.tgz"
  integrity sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q=

y18n@^5.0.5:
  version "5.0.8"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/y18n/-/y18n-5.0.8.tgz"
  integrity sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=

yallist@^3.0.2:
  version "3.1.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/yallist/-/yallist-3.1.1.tgz"
  integrity sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=

yallist@4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/yallist/-/yallist-4.0.0.tgz"
  integrity sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=

yargs-parser@^21.1.1, yargs-parser@21.1.1:
  version "21.1.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/yargs-parser/-/yargs-parser-21.1.1.tgz"
  integrity sha1-kJa87r+ZDSG7MfqVFuDt4pSnfTU=

yargs@^17.3.1, yargs@^17.7.2:
  version "17.7.2"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/yargs/-/yargs-17.7.2.tgz"
  integrity sha1-mR3zmspnWhkrgW4eA2P5110qomk=
  dependencies:
    cliui "^8.0.1"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.3"
    y18n "^5.0.5"
    yargs-parser "^21.1.1"

yn@3.1.1:
  version "3.1.1"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/yn/-/yn-3.1.1.tgz"
  integrity sha1-HodAGgnXZ8HV6rJqbkwYUYLS61A=

yocto-queue@^0.1.0:
  version "0.1.0"
  resolved "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/yocto-queue/-/yocto-queue-0.1.0.tgz"
  integrity sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=
