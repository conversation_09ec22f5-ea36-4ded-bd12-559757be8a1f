import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsString } from 'class-validator';

export class OwnerDto {
  @ApiProperty({
    description: 'Full name of the owner',
    example: '<PERSON>',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Global identifier for the owner in the organization system',
    example: 12345,
  })
  @IsNumber()
  globalId: number;

  @ApiProperty({
    description: 'Email address of the owner',
    example: '<EMAIL>',
  })
  @IsString()
  email: string;
}
