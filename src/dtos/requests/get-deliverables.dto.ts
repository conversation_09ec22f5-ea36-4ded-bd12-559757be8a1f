import { ApiPropertyOptional } from '@nestjs/swagger';
import { TransformBoolean } from '@ghq-abi/northstar-api-libs';
import { IsBoolean, IsIn, IsOptional, IsString } from 'class-validator';
import { PaginatedRequest } from '../paginated.dto';
import { Transform } from 'class-transformer';

export class GetDeliverablesRequestDto extends PaginatedRequest {
  @ApiPropertyOptional({
    description: 'Text search term to filter deliverables and projects by name',
    example: 'revenue',
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    description: 'Array of business function names to filter by',
    type: [String],
    isArray: true,
    example: ['Finance', 'Marketing', 'Operations'],
  })
  @IsOptional()
  businessFunctions?: string[];

  @ApiPropertyOptional({
    description:
      'Filter by active status. True for active items, false for inactive, omit for all',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(TransformBoolean)
  isActive?: boolean;

  @ApiPropertyOptional({
    description: 'Deliverable Type Criteria',
    type: [String],
    enum: [
      'KPI',
      'PROJECT_YES_NO',
      'PROJECT',
      'SCOPED_PROJECT_YES_NO',
      'MASTER_PROJECT',
    ],
    isArray: true,
    example: ['PROJECT_YES_NO', 'PROJECT', 'MASTER_PROJECT'],
  })
  @IsOptional()
  @IsIn(
    [
      'KPI',
      'PROJECT_YES_NO',
      'PROJECT',
      'SCOPED_PROJECT_YES_NO',
      'MASTER_PROJECT',
    ],
    { each: true },
  )
  deliverableTypes?: (
    | 'KPI'
    | 'PROJECT_YES_NO'
    | 'PROJECT'
    | 'SCOPED_PROJECT_YES_NO'
    | 'MASTER_PROJECT'
  )[];

  @ApiPropertyOptional({
    description: 'Sort Criteria',
    type: String,
    enum: ['ASC', 'DESC'],
    example: 'ASC',
  })
  @IsOptional()
  @IsIn(['ASC', 'DESC'], { each: true })
  sortType?: 'ASC' | 'DESC';
}
