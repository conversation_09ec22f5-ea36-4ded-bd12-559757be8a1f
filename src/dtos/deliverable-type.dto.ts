import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional } from 'class-validator';

export class DeliverableTypeDto {
  @ApiProperty({
    description: 'Unique code identifier for the deliverable type',
    example: 'KPI',
  })
  @IsString()
  code: string;

  @ApiPropertyOptional({
    description: 'Display name of the deliverable type',
    example: 'Key Performance Indicator',
  })
  @IsOptional()
  @IsString()
  name?: string;
}
