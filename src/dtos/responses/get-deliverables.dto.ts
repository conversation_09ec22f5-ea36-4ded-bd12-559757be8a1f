import { ApiProperty } from '@nestjs/swagger';
import { BaseListResponse } from '../base-list.dto';
import { DeliverableDto } from '../deliverable.dto';

export class GetDeliverablesResponseDto extends BaseListResponse<DeliverableDto> {
  @ApiProperty({
    description: 'Array of deliverable data items for the current page',
    type: [DeliverableDto],
    isArray: true,
  })
  data: DeliverableDto[];
}
