import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException } from '@nestjs/common';
import { AppService } from './app.service';
import {
  DeliverableRepository,
  DeliverableTypeRepository,
  EmployeeRepository,
} from '@ghq-abi/northstar-domain';

describe('AppService', () => {
  let service: AppService;
  let deliverableRepository: any;
  let deliverableTypeRepository: any;
  let employeeRepository: any;

  beforeEach(async () => {
    const mockDeliverableRepository = {
      repository: {
        findOne: jest.fn(),
        save: jest.fn(),
        create: jest.fn(),
      },
      findByUid: jest.fn(),
      findCompactStandaloneWithFilters: jest.fn(),
      findByUids: jest.fn(),
      delete: jest.fn(),
    } as any;

    const mockDeliverableTypeRepository = {
      findByCode: jest.fn(),
    } as any;

    const mockEmployeeRepository = {
      findByGlobalId: jest.fn(),
      findByName: jest.fn(),
      findById: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AppService,
        {
          provide: DeliverableRepository,
          useValue: mockDeliverableRepository,
        },
        {
          provide: DeliverableTypeRepository,
          useValue: mockDeliverableTypeRepository,
        },
        {
          provide: EmployeeRepository,
          useValue: mockEmployeeRepository,
        },
      ],
    }).compile();

    service = module.get<AppService>(AppService);
    deliverableRepository = module.get(DeliverableRepository);
    deliverableTypeRepository = module.get(DeliverableTypeRepository);
    employeeRepository = module.get(EmployeeRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getDeliverableEntity', () => {
    it('should transform deliverableType object to type string', async () => {
      const mockDeliverable = {
        uid: 'test-uid',
        name: 'Test Deliverable',
        businessFunction: 'Finance',
        frequency: 'Monthly',
        isActive: true,
        calculationMethod: 'Sum',
        definition: 'Test definition',
        paValue: '$10M',
        dateStart: new Date('2024-01-01'),
        dateEnd: new Date('2024-12-31'),
        createdAt: new Date(),
        createdBy: 'user-uuid',
        deliverableType: {
          code: 'PROJECT_YES_NO',
          name: 'Project Yes/No',
        },
        deliverables: [],
      };

      (deliverableRepository.repository.findOne as jest.Mock).mockResolvedValue(mockDeliverable);

      const result = await service.getDeliverableEntity('test-uid');

      expect(result).toEqual({
        uid: 'test-uid',
        name: 'Test Deliverable',
        businessFunction: 'Finance',
        frequency: 'Monthly',
        isActive: true,
        calculationMethod: 'Sum',
        definition: 'Test definition',
        paValue: '$10M',
        dateStart: new Date('2024-01-01'),
        dateEnd: new Date('2024-12-31'),
        createdAt: mockDeliverable.createdAt,
        createdBy: 'user-uuid',
        type: 'PROJECT_YES_NO',
        deliverables: [],
      });

      expect(deliverableRepository.repository.findOne).toHaveBeenCalledWith({
        where: { uid: 'test-uid' },
        relations: ['deliverableType', 'deliverables'],
      });
    });

    it('should handle deliverable with no type', async () => {
      const mockDeliverable = {
        uid: 'test-uid',
        name: 'Test Deliverable',
        businessFunction: 'Finance',
        frequency: 'Monthly',
        isActive: true,
        calculationMethod: 'Sum',
        definition: 'Test definition',
        paValue: '$10M',
        dateStart: new Date('2024-01-01'),
        dateEnd: new Date('2024-12-31'),
        createdAt: new Date(),
        createdBy: 'user-uuid',
        deliverableType: null,
        deliverables: [],
      };

      (deliverableRepository.repository.findOne as jest.Mock).mockResolvedValue(mockDeliverable);

      const result = await service.getDeliverableEntity('test-uid');

      expect(result.type).toBeUndefined();
    });

    it('should throw NotFoundException when deliverable not found', async () => {
      (deliverableRepository.repository.findOne as jest.Mock).mockResolvedValue(null);

      await expect(service.getDeliverableEntity('non-existent-uid')).rejects.toThrow(
        NotFoundException,
      );
    });
  });
});
