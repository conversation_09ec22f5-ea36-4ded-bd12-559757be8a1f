import { Injectable, NotFoundException } from '@nestjs/common';
import {
  DeliverableRepository,
  DeliverableTypeRepository,
  EmployeeEntity,
  EmployeeRepository,
  SortType,
} from '@ghq-abi/northstar-domain';
import { GetDeliverableResponseDto } from './dtos/responses/get-deliverable.dto';
import { GetDeliverablesResponseDto } from './dtos/responses/get-deliverables.dto';
import { CreateDeliverableRequestDto } from './dtos/requests/create-deliverable.dto';
import { UpdateDeliverableRequestDto } from './dtos/requests/update-deliverable.dto';
import { GetDeliverablesRequestDto } from './dtos/requests/get-deliverables.dto';
import { DeliverableDto } from './dtos/deliverable.dto';
import { GetEmployeesResponse } from './dtos/responses/get-employees.dto';
import { GetEmployeeResponse } from './dtos/responses/get-employee.dto';
import { GetEmployeesRequest } from './dtos/requests/get-employees.dto';

@Injectable()
export class AppService {
  constructor(
    private readonly deliverableRepository: DeliverableRepository,
    private readonly deliverableTypeRepository: DeliverableTypeRepository,
    private readonly employeeRepository: EmployeeRepository,
  ) {}

  private normalizeFilters(
    filters: GetDeliverablesRequestDto,
  ): GetDeliverablesRequestDto {
    if (typeof filters.businessFunctions === 'string') {
      try {
        const parsed = JSON.parse(filters.businessFunctions);
        if (Array.isArray(parsed)) {
          filters.businessFunctions = parsed.map(String);
        } else {
          filters.businessFunctions = [String(filters.businessFunctions)];
        }
      } catch {
        filters.businessFunctions = [String(filters.businessFunctions)];
      }
    } else if (
      filters.businessFunctions != null &&
      !Array.isArray(filters.businessFunctions)
    ) {
      filters.businessFunctions = [String(filters.businessFunctions)];
    }
    return filters;
  }

  getHealthCheck(): string {
    return 'OK';
  }

  async createDeliverableEntity(
    deliverableDto: CreateDeliverableRequestDto,
    sessionUserUuid: string,
  ): Promise<GetDeliverableResponseDto> {
    let deliverablesEntities = [];

    if (
      deliverableDto.deliverableUids &&
      deliverableDto.deliverableUids.length > 0
    ) {
      deliverablesEntities = await this.deliverableRepository.findByUids(
        deliverableDto.deliverableUids,
      );

      const foundUids = deliverablesEntities.map((d) => d.uid);
      const missingUids = deliverableDto.deliverableUids.filter(
        (uid) => !foundUids.includes(uid),
      );

      if (missingUids.length > 0) {
        throw new NotFoundException(
          `Deliverable(s) with uid(s) ${missingUids.join(', ')} not found.`,
        );
      }
    }

    // Handle type if provided
    let deliverableType = null;
    if (deliverableDto.type) {
      deliverableType = await this.deliverableTypeRepository.findByCode(
        deliverableDto.type,
      );
      if (!deliverableType) {
        throw new NotFoundException(
          `Deliverable type with code ${deliverableDto.type} not found.`,
        );
      }
    }

    if (deliverableDto?.scopedDeliverables?.length > 0) {
      const scopedDeliverablesEntities = await Promise.all(
        deliverableDto.scopedDeliverables.map(async (scopedDeliverable) => {
          return this.createDeliverableEntity(
            scopedDeliverable,
            sessionUserUuid,
          );
        }),
      );

      deliverableDto.deliverableUids = [
        ...(deliverableDto.deliverableUids || []),
        ...scopedDeliverablesEntities.map((d) => d.uid),
      ];
      deliverablesEntities.push(...scopedDeliverablesEntities);
    }

    const entity = this.deliverableRepository.repository.create({
      buLevelAggregation: deliverableDto.buLevelAggregation,
      businessFunction: deliverableDto.businessFunction,
      calculationMethod: deliverableDto.calculationMethod,
      dateEnd: deliverableDto.dateEnd,
      dateStart: deliverableDto.dateStart,
      definition: deliverableDto.definition,
      frequency: deliverableDto.frequency,
      isActive: true,
      name: deliverableDto.name,
      paValue: deliverableDto.paValue,
      dataSource: deliverableDto.dataSource,
      deliverables: deliverablesEntities,
      deliverableType: deliverableType,
      createdBy: sessionUserUuid,
    });

    return await this.deliverableRepository.repository.save(entity);
  }

  async updateDeliverableEntity(
    uid: string,
    deliverableDto: UpdateDeliverableRequestDto,
    sessionUserUuid: string,
  ): Promise<GetDeliverableResponseDto> {
    const deliverable = await this.deliverableRepository.findByUid(uid);
    if (!deliverable) {
      throw new NotFoundException(`Deliverable with uid ${uid} not found.`);
    }

    let deliverablesEntities = deliverable.deliverables;

    if (deliverableDto.deliverableUids !== undefined) {
      if (deliverableDto.deliverableUids.length > 0) {
        deliverablesEntities = await this.deliverableRepository.findByUids(
          deliverableDto.deliverableUids,
        );

        const foundUids = deliverablesEntities.map((d) => d.uid);
        const missingUids = deliverableDto.deliverableUids.filter(
          (uid) => !foundUids.includes(uid),
        );

        if (missingUids.length > 0) {
          throw new NotFoundException(
            `Deliverable(s) with uid(s) ${missingUids.join(', ')} not found.`,
          );
        }
      } else {
        deliverablesEntities = [];
      }
    }

    // Extract type and deliverableUids from the DTO to handle them separately
    const { type, ...updateFields } = deliverableDto;

    // Apply the update fields to the entity
    Object.assign(deliverable, {
      ...updateFields,
      deliverables: deliverablesEntities,
      // updatedAt is automatically handled by @UpdateDateColumn
      updatedBy: sessionUserUuid,
    });

    // Handle type separately if provided
    if (type !== undefined) {
      if (type) {
        // Find the deliverable type by code
        const deliverableType =
          await this.deliverableTypeRepository.findByCode(type);

        if (!deliverableType) {
          throw new NotFoundException(
            `Deliverable type with code ${type} not found.`,
          );
        }

        deliverable.deliverableType = deliverableType;
      } else {
        // If type is null or empty, remove the relationship
        deliverable.deliverableType = null;
      }
    }

    return await this.deliverableRepository.repository.save(deliverable);
  }

  async getDeliverableEntity(uid: string): Promise<GetDeliverableResponseDto> {
    const deliverable = await this.deliverableRepository.repository.findOne({
      where: { uid },
      relations: ['deliverableType', 'deliverables'],
    });

    if (!deliverable) {
      throw new NotFoundException(`Deliverable with uid ${uid} not found.`);
    }

    const { deliverableType, ...deliverableData } = deliverable as any;
    return {
      ...deliverableData,
      type: deliverableType?.code,
    } as any;
  }

  async getAllDeliverableEntities(
    filters: GetDeliverablesRequestDto,
    sessionUserUuid: string,
  ): Promise<GetDeliverablesResponseDto> {
    console.log(sessionUserUuid);

    const { search, businessFunctions, isActive, sortType, deliverableTypes } =
      this.normalizeFilters(filters);

    const entities =
      await this.deliverableRepository.findCompactStandaloneWithFilters(
        search,
        businessFunctions,
        isActive,
        Array.isArray(deliverableTypes) ? deliverableTypes : [deliverableTypes],
        sortType as SortType,
        filters.pageNumber,
        filters.pageSize,
      );

    return {
      data: entities['data'] as DeliverableDto[],
      pageNumber: entities['pageNumber'],
      pageSize: entities['pageSize'],
      totalRecords: entities['totalRecords'],
    };
  }

  async getEmployees(
    filters: GetEmployeesRequest,
    sessionUserUuid: string,
  ): Promise<GetEmployeesResponse> {
    console.log(sessionUserUuid);
    let employees: EmployeeEntity[];
    let totalRecords: number;

    const { search } = filters;
    const isSearchNumberType = !isNaN(Number(search));

    if (isSearchNumberType) {
      const employee = await this.employeeRepository.findByGlobalId(
        Number(search),
      );
      employees = employee ? [employee] : [];
      totalRecords = 1;
    } else {
      employees = await this.employeeRepository.findByName(search);
      totalRecords = employees.length;
    }

    const mappedEmployees = employees.map((emp) => ({
      uuid: emp.uuid,
      email: emp.email,
      firstName: emp.firstname,
      lastName: emp.lastname,
      positionTitle: emp.positionTitle,
      globalId: emp.globalId,
    }));

    return {
      data: mappedEmployees,
      pageNumber: 1,
      pageSize: totalRecords,
      totalRecords,
    };
  }

  async getEmployee(
    uuid: string,
    sessionUserUuid: string,
  ): Promise<GetEmployeeResponse> {
    console.log(sessionUserUuid);
    const employee = await this.employeeRepository.findById(uuid);

    const mappedEmployee = {
      uuid: employee.uuid,
      email: employee.email,
      firstName: employee.firstname,
      lastName: employee.lastname,
      positionTitle: employee.positionTitle,
      globalId: employee.globalId,
    };

    return mappedEmployee;
  }

  // Legacy methods - keeping for backward compatibility if needed
  async createDeliverable(
    deliverableDto: CreateDeliverableRequestDto,
    sessionUserUuid: string,
  ): Promise<GetDeliverableResponseDto> {
    // Delegate to the new method
    return this.createDeliverableEntity(deliverableDto, sessionUserUuid);
  }

  async updateDeliverable(
    uid: string,
    deliverableDto: UpdateDeliverableRequestDto,
    sessionUserUuid: string,
  ): Promise<GetDeliverableResponseDto> {
    // Delegate to the new method
    return this.updateDeliverableEntity(uid, deliverableDto, sessionUserUuid);
  }

  async softDeleteDeliverable(
    uid: string,
    sessionUserUuid: string,
  ): Promise<GetDeliverableResponseDto> {
    const deliverable = await this.deliverableRepository.findByUid(uid);

    if (!deliverable) {
      throw new NotFoundException(`Deliverable with uid ${uid} not found.`);
    }

    await this.deliverableRepository.delete(uid, sessionUserUuid);

    return deliverable;
  }

  async getDeliverablesFunctions(): Promise<string[]> {
    const response = await this.deliverableRepository.getFunctions();

    if (!response || response.length === 0) {
      return [];
    }

    return response.map((item) => item.function);
  }
}
