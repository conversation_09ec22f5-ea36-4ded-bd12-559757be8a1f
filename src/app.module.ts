import {
  MiddlewareConsumer,
  Module,
  NestModule,
  RequestMethod,
} from '@nestjs/common';
import { AppController } from './app.controller';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ExtraLogMiddleware } from '@ghq-abi/northstar-api-libs';
import { DataSourceOptions } from 'typeorm';
import {
  DeliverableEntity,
  DeliverableOwnerEntity,
  DeliverableRepository,
  DeliverableTypeRepository,
  DeliverableTypeEntity,
  EmployeeEntity,
  EmployeeRepository,
} from '@ghq-abi/northstar-domain';
import { typeorm } from './configs/typeorm.config';
import { AppService } from './app.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [typeorm],
    }),
    TypeOrmModule.forRootAsync({
      inject: [ConfigService],
      useFactory: async (
        configService: ConfigService,
      ): Promise<DataSourceOptions> =>
        configService.get<DataSourceOptions>('typeorm'),
    }),
    TypeOrmModule.forFeature([
      DeliverableEntity,
      DeliverableOwnerEntity,
      DeliverableTypeEntity,
      EmployeeEntity,
    ]),
  ],
  controllers: [AppController],
  providers: [
    AppService,
    ExtraLogMiddleware,
    DeliverableRepository,
    DeliverableTypeRepository,
    EmployeeRepository,
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(ExtraLogMiddleware).exclude({
      method: RequestMethod.GET,
      path: '*',
    });
  }
}
