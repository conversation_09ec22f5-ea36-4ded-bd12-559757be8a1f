import { registerAs } from '@nestjs/config';
import { config as dotenvConfig } from 'dotenv';
import { DataSource, DataSourceOptions } from 'typeorm';
import {
  DeliverableEntity,
  DeliverableOwnerEntity,
  DeliverableTypeEntity,
  EmployeeEntity,
} from '@ghq-abi/northstar-domain';

dotenvConfig({ path: '.env' });

const config: DataSourceOptions = {
  type: 'mssql',
  host: `${process.env.DATABASE_HOST}`,
  port: +process.env.DATABASE_PORT,
  username: `${process.env.DATABASE_USERNAME}`,
  password: `${process.env.DATABASE_PASSWORD}`,
  database: `${process.env.DATABASE_NAME}`,
  schema: `${process.env.DATABASE_SCHEMA}`,
  synchronize: false,
  options: { encrypt: true },
  entities: [
    DeliverableEntity,
    DeliverableOwnerEntity,
    DeliverableTypeEntity,
    EmployeeEntity,
  ],
  migrations: ['./src/migrations/*.{ts,js}'],
  requestTimeout: 30000,
  connectionTimeout: 30000,
};

// Export the configuration for NestJS
export const typeorm = registerAs('typeorm', () => config);

// Export DataSource for migrations and CLI tools
export const dataSource = new DataSource(config);
